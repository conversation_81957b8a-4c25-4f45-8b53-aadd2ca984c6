<template>
  <div class="keep-alive-test">
    <van-nav-bar title="Keep-Alive 测试" left-arrow @click-left="$router.back()" />
    
    <div class="test-content">
      <div class="test-section">
        <h3>组件实例创建次数测试</h3>
        <p>如果 keep-alive 正常工作，每个组件的创建次数应该为 1</p>
        
        <div class="test-results">
          <div v-for="(count, name) in instanceCounts" :key="name" class="test-item">
            <span class="component-name">{{ name }}:</span>
            <span class="count">{{ count }} 次</span>
            <span :class="['status', count === 1 ? 'success' : 'error']">
              {{ count === 1 ? '✅ 正常缓存' : '❌ 可能未缓存' }}
            </span>
          </div>
        </div>
      </div>
      
      <div class="test-section">
        <h3>测试步骤</h3>
        <ol>
          <li>访问首页 (Home)</li>
          <li>访问定单列表 (BookingList)</li>
          <li>点击"新增定单"进入 BookingCreate 页面</li>
          <li>返回定单列表</li>
          <li>返回首页</li>
          <li>再次访问定单列表</li>
          <li>查看此页面的测试结果和控制台输出</li>
        </ol>

        <div class="test-tips">
          <h4>判断标准：</h4>
          <ul>
            <li>✅ 正常缓存：组件只挂载1次，有 activated/deactivated 事件</li>
            <li>❌ 缓存失效：组件挂载多次，或者被卸载</li>
          </ul>
        </div>
      </div>
      
      <div class="test-actions">
        <van-button type="primary" @click="refreshReport" block>
          刷新测试报告
        </van-button>
        <van-button type="warning" @click="resetTest" block style="margin-top: 16px;">
          重置测试计数
        </van-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'KeepAliveTest'
}
</script>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { KeepAliveTest } from '../utils/keep-alive-test'
import { KeepAliveDebug } from '../utils/keep-alive-debug'

const instanceCounts = ref<Record<string, number>>({})

const refreshReport = () => {
  // 获取所有组件的实例计数
  const components = ['Home', 'BookingList', 'BookingCreate', 'EntryManagement', 'ExitManagement', 'RoomStateDiagram']
  const counts: Record<string, number> = {}

  components.forEach(name => {
    counts[name] = KeepAliveTest.getInstanceCount(name)
  })

  instanceCounts.value = counts
  KeepAliveTest.printReport()
  KeepAliveDebug.printDetailedReport()
}

const resetTest = () => {
  KeepAliveTest.reset()
  KeepAliveDebug.reset()
  instanceCounts.value = {}
}

onMounted(() => {
  refreshReport()
})
</script>

<style scoped>
.keep-alive-test {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.test-content {
  padding: 16px;
}

.test-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.test-section h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 18px;
}

.test-section p {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 14px;
}

.test-results {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-item {
  display: flex;
  align-items: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.component-name {
  font-weight: 500;
  min-width: 120px;
}

.count {
  margin: 0 12px;
  font-family: monospace;
}

.status.success {
  color: #52c41a;
}

.status.error {
  color: #ff4d4f;
}

.test-section ol {
  margin: 0;
  padding-left: 20px;
}

.test-section li {
  margin-bottom: 4px;
  color: #666;
}

.test-actions {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.test-tips {
  margin-top: 16px;
  padding: 12px;
  background: #f0f9ff;
  border-left: 4px solid #1989fa;
  border-radius: 4px;
}

.test-tips h4 {
  margin: 0 0 8px 0;
  color: #1989fa;
  font-size: 16px;
}

.test-tips ul {
  margin: 0;
  padding-left: 20px;
}

.test-tips li {
  margin-bottom: 4px;
  color: #666;
}
</style>
