# 进场管理API接口测试

## 接口更新说明

根据接口文档，已更新以下接口：

### 1. 进场记录列表查询 
- **接口地址**: `POST /enter/list`
- **参数类型**: `EnterQueryDTO`
- **返回类型**: `EnterVo[]`

### 2. 未进场房源查询
- **接口地址**: `POST /enter/unenteredRooms`  
- **参数类型**: `EnterQueryDTO`
- **返回类型**: `RoomInfo[]`

### 3. 进场单初始化
- **接口地址**: `POST /enter/init`
- **参数类型**: `EnterInitDTO`

## 数据结构变更

### EnterQueryDTO (查询参数)
```typescript
interface EnterQueryDTO {
    pageNum: number;
    pageSize: number;
    type?: string; // 0-待办理,1-已办理
    tenantName?: string; // 承租方名称，模糊匹配
    roomName?: string; // 楼栋/房源名称，模糊搜索
    roomAndCustomerName?: string; // 楼栋/房源/承租方
    nearDays?: number; // 合同开始近3/7/15天
    rentStartDateBegin?: string; // 租期开始日期
    rentStartDateEnd?: string; // 租期结束日期
    projectId?: string;
    contractId?: string;
    createByName?: string;
}
```

### EnterVo (列表项)
```typescript
interface EnterVo {
    id: string;
    projectId: string;
    contractId: string;
    contractNo: string; // 合同编号
    tenantName: string; // 承租方名称
    roomName: string; // 房源名称
    rentStartDate: string; // 租期开始日期
    rentEndDate: string; // 租期结束日期
    unenterNum: number; // 未进场房源数
    enteredRoomCount: number; // 已进场房源数
    createByName: string;
    createTime: string;
    // 前端计算字段
    status?: number; // 0-待办理 1-已办理
    statusName?: string;
}
```

## 主要变更内容

### 1. 字段映射变更
- `customerName` → `tenantName` (承租方名称)
- `entryDate` → `rentStartDate` + `rentEndDate` (租期)
- `pendingRooms` → `unenterNum` (未进场房源数)

### 2. 状态计算逻辑
```typescript
// 根据未进场房源数判断状态
item.status = item.unenterNum > 0 ? 0 : 1 // 0-待办理 1-已办理
item.statusName = item.status === 0 ? '待办理' : '已办理'
```

### 3. 搜索参数调整
- `searchType` → `type` (状态筛选)
- `customerName` → `tenantName` (承租方搜索)
- `entryDateStart/End` → `rentStartDateBegin/End` (租期筛选)
- 新增 `roomAndCustomerName` (综合搜索)
- 新增 `nearDays` (时间筛选)

### 4. 房源选择功能
- 使用 `getUnenteredRooms` 获取未进场房源
- 使用 `initEntry` 初始化进场单
- 传递 `contractId` 而不是 `entryId`

## 测试要点

### 1. 列表查询测试
```javascript
// 测试参数
const params = {
    pageNum: 1,
    pageSize: 10,
    type: "0", // 待办理
    tenantName: "测试公司",
    projectId: "108"
}
```

### 2. 房源选择测试
```javascript
// 获取未进场房源
const roomParams = {
    pageNum: 1,
    pageSize: 100,
    contractId: "contract123"
}

// 初始化进场单
const initParams = {
    contractId: "contract123",
    roomIds: ["room1", "room2"]
}
```

### 3. 状态筛选测试
- 待办理：`type: "0"`
- 已办理：`type: "1"`

### 4. 时间筛选测试
- 近3天：`nearDays: 3`
- 近7天：`nearDays: 7`
- 近30天：`nearDays: 30`

## 注意事项

1. **接口地址变更**：从 `/business-rent-rest/enter/` 改为 `/enter/`
2. **数据结构适配**：前端需要适配新的字段名称
3. **状态计算**：状态通过 `unenterNum` 计算，不是后端直接返回
4. **向后兼容**：保留了类型别名确保代码兼容性
5. **错误处理**：需要适配新的响应数据结构 