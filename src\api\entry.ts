import http from "./index";

// 进场通知单查询参数
export interface EntryDetailParams {
    contractId: string;
}

// 进场管理列表查询参数（根据接口文档EnterQueryDTO）
export interface EnterQueryDTO {
    params?: Record<string, any>;
    pageNum: number;
    pageSize: number;
    id?: string;
    projectId?: string;
    contractId?: string;
    contractUnionId?: string;
    enteredRoomCount?: number;
    isNotify?: boolean;
    createBy?: string;
    createByName?: string;
    createTime?: string;
    updateBy?: string;
    updateByName?: string;
    updateTime?: string;
    isDel?: boolean;
    type?: string; // 类型: 0-待办理,1-已办理
    tenantName?: string; // 承租方名称，模糊匹配
    roomName?: string; // 楼栋或者房源名称，模糊搜索
    rentStartDateBegin?: string; // 租期开始日期
    rentStartDateEnd?: string; // 租期结束日期
    roomAndCustomerName?: string; // 楼栋/房源/承租方
    nearDays?: number; // 合同开始近3/7/15天
}

// 进场管理列表项（根据接口文档EnterVo）
export interface EnterVo {
    id: string;
    projectId: string;
    contractId: string;
    contractUnionId: string;
    enteredRoomCount: number; // 已进场房源数
    isNotify: boolean;
    createByName: string;
    updateByName: string;
    isDel: boolean;
    projectName: string;
    contractNo: string;
    contractPurpose: number;
    tenantName: string; // 承租方名称
    roomName: string; // 房源名称
    rentStartDate: string; // 租期开始日期
    rentEndDate: string; // 租期结束日期
    unenterNum: number; // 未进场房源数
    createTime: string;
    // 添加状态相关字段（前端处理）
    statusName?: string;
    status?: number; // 0-待办理 1-已办理（通过unenterNum计算）
}

// 进场单初始化参数（根据接口文档EnterInitDTO）
export interface EnterInitDTO {
    contractId: string;
    roomIds: string[];
}

// 进场房间添加DTO（根据接口文档EnterRoomAddDTO）
export interface EnterRoomAddDTO {
    id?: string;
    enterId?: string;
    roomId: string;
    roomName: string;
    propertyType?: number;
    parcelName?: string;
    buildingName?: string;
    enterDate?: string;
    elecMeterReading?: number;
    coldWaterReading?: number;
    hotWaterReading?: number;
    remark?: string;
    assetList?: EnterRoomAssetsAddDTO[];
    isDel?: boolean;
}

// 进场房间资产DTO
export interface EnterRoomAssetsAddDTO {
    id?: string;
    enterRoomId?: string;
    category?: number;
    name?: string;
    specification?: string;
    count?: number;
    isMissing?: boolean;
    isAdd?: boolean;
    isDel?: boolean;
}

// 进场单添加DTO（根据接口文档EnterAddDTO）
export interface EnterAddDTO {
    id?: string;
    projectId?: string;
    contractId?: string;
    contractUnionId?: string;
    enteredRoomCount?: number;
    isNotify?: boolean;
    isDel?: boolean;
    roomList?: EnterRoomAddDTO[];
}

// 为了向后兼容，保留原有接口
export type EntryListParams = EnterQueryDTO;
export type EntryListItem = EnterVo;
export interface EntryListResponse {
    code: number;
    data: EnterVo[];
    total?: number;
    msg: string;
}

// 合同信息
export interface ContractInfo {
    id: string;
    contractNo: string;
    projectId: string;
    projectName: string;
    customerId: string;
    customerName: string;
    lesseeName: string;
    contractType: number;
    signDate: string;
    startDate: string;
    endDate: string;
    status: number;
    createTime: string;
    updateTime?: string;
}

// 房间信息
export interface RoomInfo {
    id: string;
    roomNo: string;
    roomName: string;
    roomType: string;
    buildingId: string;
    buildingName: string;
    floorId: string;
    floorName: string;
    area: number;
    rentPrice: number;
    status: number;
    isEntered: boolean;
    enteredDate?: string;
}

// 进场通知单详情信息
export interface EntryDetailVo {
    id: string;
    entryNo: string;
    projectId: string;
    projectName: string;
    contractId: string;
    contractInfo: ContractInfo;
    customerId: string;
    customerName: string;
    lesseeName: string;
    entryDate: string;
    totalRooms: number;
    enteredRooms: number;
    pendingRooms: number;
    roomList: RoomInfo[];
    status: number;
    remark?: string;
    createBy: string;
    createByName: string;
    createTime: string;
    updateBy?: string;
    updateByName?: string;
    updateTime?: string;
}

// 进场通知单状态枚举
export const ENTRY_STATUS = {
    DRAFT: 0, // 草稿
    PENDING: 1, // 待进场
    PARTIAL: 2, // 部分进场
    COMPLETED: 3, // 已完成
    CANCELLED: 4, // 已取消
} as const;

// 房间状态枚举
export const ROOM_STATUS = {
    AVAILABLE: 0, // 可用
    OCCUPIED: 1, // 已占用
    MAINTENANCE: 2, // 维护中
    RESERVED: 3, // 已预订
} as const;

// 合同类型枚举
export const CONTRACT_TYPE = {
    RENT: 1, // 租赁合同
    LEASE: 2, // 承租合同
    SUBLEASE: 3, // 转租合同
} as const;

/**
 * C端进场通知单详情接口
 * 根据进场单id查询进场通知单详情信息，包括合同信息、房间列表和已进场房间数
 */
export function getEntryDetail(params: EntryDetailParams) {
    return http.get<EntryDetailVo>("/business-rent-rest/enter/detail", params);
}

/**
 * 进场管理列表查询接口
 * 根据查询条件获取进场管理列表数据
 */
export function getEntryList(params: EnterQueryDTO) {
    return http.post<{ data: EnterVo[]; total?: number; msg?: string }>("/business-rent-rest/enter/list", params);
}

/**
 * 根据合同id查询未进场房源列表
 * 获取指定合同下的待进场房源列表
 */
export function getUnenteredRooms(params: EnterQueryDTO) {
    return http.post<RoomInfo[]>("/business-rent-rest/enter/unenteredRooms", params);
}

/**
 * 进场单初始化
 * 根据合同ID和房间ID集合初始化进场单
 */
export function initEntry(data: EnterInitDTO) {
    return http.post("/business-rent-rest/enter/init", data);
}

/**
 * 进场单保存
 * 保存进场单信息
 */
export function saveEntry(data: EnterAddDTO) {
    return http.post("/business-rent-rest/enter/save", data);
}

/**
 * 通知客户
 * 根据进场单id发送进场通知给客户
 */
export function notifyCustomer(enterId: string) {
    return http.get("/business-rent-rest/enter/notifyCustomer", { enterId });
}

/**
 * 进场单详情（包含房间和资产信息）
 * 根据进场单id查询进场单详情信息，包括合同信息、房间列表和资产列表
 */
export function getEnterDetail(enterId: string) {
    return http.get("/business-rent-rest/enter/enterDetail", { enterId });
}

// 为了向后兼容，保留原有接口别名
export const getEntryRooms = getUnenteredRooms;
export const handleEntry = initEntry;
