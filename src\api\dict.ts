import request from './index'

// 字典项数据结构
export interface DictOption {
  code: string
  name: string
  value?: string
  children?: DictOption[]
  level?: number
  parentCode?: string
  remark?: string
  sort?: number
  status?: number
}

// 字典数据响应类型
export interface DictResponse {
  code: number
  msg: string
  data: DictOption[]
}

/**
 * 根据字典类型获取字典数据
 * @param dictType 字典类型编码
 */
export const getDictByType = (dictType: string) => {
  return request.get<DictResponse>('/business-platform/system/dict/data/type', { dictType })
}

/**
 * 获取物业类型字典（二级联动）
 * 返回带有子级的物业类型数据
 */
export const getPropertyTypeDict = () => {
  return request.get<DictResponse>('/system/dict/data/propertyType')
}

/**
 * 根据父级编码获取子级字典数据
 * @param parentCode 父级编码
 */
export const getDictByParentCode = (parentCode: string) => {
  return request.get<DictResponse>('/system/dict/data/children', { parentCode })
}

// 物业类型常量定义（作为本地备用数据）
export const PROPERTY_TYPE_OPTIONS: DictOption[] = [
  {
    code: 'dormitory',
    name: '宿舍',
    children: [
      { code: 'dormitory_standard', name: '标准宿舍', parentCode: 'dormitory' },
      { code: 'dormitory_apartment', name: '公寓式宿舍', parentCode: 'dormitory' },
      { code: 'dormitory_single', name: '单人宿舍', parentCode: 'dormitory' },
      { code: 'dormitory_double', name: '双人宿舍', parentCode: 'dormitory' }
    ]
  },
  {
    code: 'office',
    name: '办公',
    children: [
      { code: 'office_standard', name: '标准办公', parentCode: 'office' },
      { code: 'office_executive', name: '行政办公', parentCode: 'office' },
      { code: 'office_creative', name: '创意办公', parentCode: 'office' },
      { code: 'office_shared', name: '共享办公', parentCode: 'office' }
    ]
  },
  {
    code: 'commercial',
    name: '商业',
    children: [
      { code: 'commercial_retail', name: '零售商业', parentCode: 'commercial' },
      { code: 'commercial_restaurant', name: '餐饮商业', parentCode: 'commercial' },
      { code: 'commercial_service', name: '服务商业', parentCode: 'commercial' },
      { code: 'commercial_entertainment', name: '娱乐商业', parentCode: 'commercial' }
    ]
  },
  {
    code: 'warehouse',
    name: '仓储',
    children: [
      { code: 'warehouse_standard', name: '标准仓储', parentCode: 'warehouse' },
      { code: 'warehouse_cold', name: '冷链仓储', parentCode: 'warehouse' },
      { code: 'warehouse_smart', name: '智能仓储', parentCode: 'warehouse' },
      { code: 'warehouse_logistics', name: '物流仓储', parentCode: 'warehouse' }
    ]
  }
]

/**
 * 根据编码获取物业类型名称
 * @param businessTypeCode 业态编码
 * @param propertyTypeOptions 物业类型选项（默认使用PROPERTY_TYPE_OPTIONS）
 */
export const getBusinessTypeName = (
  businessTypeCode: string, 
  propertyTypeOptions: DictOption[] = PROPERTY_TYPE_OPTIONS
): string => {
  if (!businessTypeCode) return ''

  // 先查找一级分类
  for (const option of propertyTypeOptions) {
    if (option.code === businessTypeCode) {
      return option.name
    }
    // 再查找二级分类
    if (option.children) {
      for (const child of option.children) {
        if (child.code === businessTypeCode) {
          return child.name
        }
      }
    }
  }
  return ''
}

/**
 * 根据一级编码获取二级选项
 * @param parentCode 一级编码
 * @param propertyTypeOptions 物业类型选项（默认使用PROPERTY_TYPE_OPTIONS）
 */
export const getChildrenByParentCode = (
  parentCode: string,
  propertyTypeOptions: DictOption[] = PROPERTY_TYPE_OPTIONS
): DictOption[] => {
  const parent = propertyTypeOptions.find(option => option.code === parentCode)
  return parent?.children || []
}

/**
 * 获取所有一级物业类型选项
 * @param propertyTypeOptions 物业类型选项（默认使用PROPERTY_TYPE_OPTIONS）
 */
export const getFirstLevelOptions = (
  propertyTypeOptions: DictOption[] = PROPERTY_TYPE_OPTIONS
): DictOption[] => {
  return propertyTypeOptions.map(option => ({
    code: option.code,
    name: option.name,
    parentCode: option.parentCode,
    level: 1
  }))
}

/**
 * 扁平化字典选项（将二级结构拍平为一维数组）
 * @param propertyTypeOptions 物业类型选项
 */
export const flattenDictOptions = (propertyTypeOptions: DictOption[]): DictOption[] => {
  const result: DictOption[] = []
  
  propertyTypeOptions.forEach(option => {
    // 添加一级选项
    result.push({
      ...option,
      level: 1,
      children: undefined // 移除children，避免循环引用
    })
    
    // 添加二级选项
    if (option.children) {
      option.children.forEach(child => {
        result.push({
          ...child,
          level: 2,
          parentCode: option.code
        })
      })
    }
  })
  
  return result
} 