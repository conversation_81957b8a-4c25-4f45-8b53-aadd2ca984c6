# 房源联动功能实现说明

## 功能概述

在BookingCreate.vue页面中实现了根据项目ID和业态类型动态获取意向房源的功能，包括：

1. **项目变化触发房源更新**：选择项目后自动获取该项目下的房源
2. **业态类型筛选**：根据选择的业态类型筛选对应的房源
3. **动态联动**：项目或业态类型变化时自动重新获取房源数据
4. **用户体验优化**：加载状态提示、错误处理、空状态处理

## 技术实现

### 1. API接口层 (`src/api/room.ts`)

创建了专门的房源API模块，包含：

```typescript
// 房源选项接口定义
export interface RoomOption {
    id: string
    code: string
    name: string
    buildingName?: string
    floorName?: string
    roomTypeName?: string
    area?: number
    status?: string | number
    [key: string]: any
}

// 查询参数接口
export interface RoomOptionsParams {
    projectId: string
    businessType?: string // 业态类型
    keyword?: string // 搜索关键词
    status?: string | number // 房源状态
    pageNum?: number
    pageSize?: number
}

// API方法
export const getRoomOptions = (params: RoomOptionsParams) => {
    return http.get<RoomOptionsResponse>('/business-rent-admin/room/roomOptions', params)
}
```

### 2. 数据状态管理

在组件中添加了房源相关的响应式数据：

```javascript
// 房源选择
const showRoomPicker = ref(false)
const roomValue = ref([])
const roomColumns = ref<Array<{text: string, value: string}>>([])
const roomList = ref<RoomOption[]>([])
const roomLoading = ref(false)
```

### 3. 核心功能方法

#### 获取房源列表方法
```javascript
const getRoomList = async (projectId: string, businessType?: string) => {
    if (!projectId) {
        roomColumns.value = []
        roomList.value = []
        return
    }
    
    try {
        roomLoading.value = true
        const loadingToast = showLoadingToast('加载房源列表...')
        
        const params: RoomOptionsParams = {
            projectId: projectId,
            pageSize: 1000 // 获取所有房源
        }
        
        // 如果有业态类型，添加到查询参数
        if (businessType) {
            params.businessType = businessType
        }
        
        const res = await getRoomOptions(params)
        
        if (res.code === 200 && Array.isArray(res.data?.list)) {
            roomList.value = res.data.list
            roomColumns.value = res.data.list.map(room => ({
                text: room.name || `${room.buildingName || ''}-${room.code || room.id}`,
                value: room.id
            }))
            
            if (roomColumns.value.length === 0) {
                showToast('暂无可选房源')
            }
        } else {
            roomColumns.value = []
            roomList.value = []
            showToast('获取房源列表为空')
        }
        
        loadingToast.close()
    } catch (error) {
        console.error('获取房源列表失败:', error)
        showToast('获取房源列表失败，请重试')
        roomColumns.value = []
        roomList.value = []
    } finally {
        roomLoading.value = false
    }
}
```

### 4. 联动触发机制

#### 项目变化触发
```javascript
const onProjectConfirm = (value: any) => {
    const oldProjectId = formData.projectId
    const newProjectId = value.selectedValues[0]
    
    formData.projectId = newProjectId
    formData.projectName = value.selectedOptions[0].text
    showProjectPicker.value = false
    
    // 项目变化时，清空房源选择并重新获取房源列表
    if (oldProjectId !== newProjectId) {
        formData.roomId = ''
        formData.room = ''
        
        // 根据当前选择的业态类型获取房源
        const currentBusinessType = formData.propertyTypeCode
        getRoomList(newProjectId, currentBusinessType)
    }
}
```

#### 业态类型变化触发
```javascript
const onBusinessTypeConfirm = (value: any) => {
    // ... 原有的业态类型处理逻辑 ...
    
    // 业态类型变化时，清空房源选择并重新获取房源列表
    if (oldBusinessType !== newBusinessType && formData.projectId) {
        formData.roomId = ''
        formData.room = ''
        getRoomList(formData.projectId, newBusinessType)
    }
}
```

### 5. 用户界面优化

#### 房源字段状态管理
```vue
<!-- 意向房源 -->
<van-field v-model="formData.room" required readonly name="room" label="意向房源" 
    :placeholder="formData.projectId ? '请选择' : '请先选择项目'" 
    input-align="right"
    :disabled="!formData.projectId"
    @click="handleRoomFieldClick">
    <template #button>
        <i class="van-icon van-icon-arrow-down"></i>
    </template>
</van-field>
```

#### 选择器加载状态
```vue
<van-picker 
    :columns="roomColumns" 
    :model-value="roomValue" 
    @confirm="onRoomConfirm"
    @cancel="showRoomPicker = false" 
    title="选择意向房源"
    :loading="roomLoading" />
```

## 功能流程

### 1. 初始状态
- 房源字段显示"请先选择项目"
- 房源字段处于禁用状态
- 房源列表为空

### 2. 选择项目后
- 自动调用 `/business-rent-admin/room/roomOptions` 接口
- 传递项目ID参数
- 如果已选择业态类型，同时传递业态类型参数
- 更新房源列表并启用房源字段

### 3. 选择业态类型后
- 如果已选择项目，重新调用房源接口
- 传递项目ID和新的业态类型参数
- 清空当前房源选择，更新房源列表

### 4. 选择房源
- 从已加载的房源列表中选择
- 存储房源ID和名称到表单数据
- 可获取房源的详细信息（建筑、楼层等）

## 错误处理

1. **网络错误**：显示"获取房源列表失败，请重试"提示
2. **空数据**：显示"暂无可选房源"提示
3. **未选择项目**：显示"请先选择项目"提示
4. **加载状态**：显示loading状态和"加载房源列表..."提示

## 数据流向

```
项目选择/业态选择 
    ↓
清空当前房源选择
    ↓
调用 getRoomOptions API
    ↓
更新 roomList 和 roomColumns
    ↓
用户选择房源
    ↓
更新 formData 中的房源信息
```

## 注意事项

1. **接口参数**：确保后端接口 `/business-rent-admin/room/roomOptions` 支持 `projectId` 和 `businessType` 参数
2. **数据格式**：房源数据应包含 `id`、`name`、`code` 等基本字段
3. **性能优化**：当前设置 `pageSize: 1000` 获取所有房源，实际使用时可根据需要调整
4. **用户体验**：项目或业态变化时会清空房源选择，提醒用户重新选择

## 扩展功能

后续可以考虑添加：

1. **房源搜索**：支持关键词搜索房源
2. **房源状态筛选**：只显示可用状态的房源
3. **房源详情展示**：显示房源的面积、类型等详细信息
4. **分页加载**：当房源数量较多时支持分页加载
5. **缓存优化**：缓存已加载的房源数据，避免重复请求

此功能实现了完整的项目-业态-房源三级联动，提供了良好的用户体验和错误处理机制。 