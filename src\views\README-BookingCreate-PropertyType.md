# BookingCreate 意向业态功能说明

## 概述

基于您的要求，我已经完善了 BookingCreate.vue 中的意向业态功能，实现了二级联动选择和基于字典 code 的数据管理。

## 功能特性

### 1. 二级联动选择
- **一级分类**：宿舍、办公、商业、仓储
- **二级分类**：根据一级分类动态显示对应的子分类
- **联动效果**：选择一级分类后，二级分类自动更新

### 2. 字典数据管理
- **API 接口**：支持从后端获取字典数据
- **本地备用**：内置默认数据，确保功能正常运行
- **编码存储**：表单存储业态编码，便于后端处理

### 3. 显示逻辑
- **显示名称**：界面显示中文名称
- **存储编码**：后台存储英文编码
- **名称转换**：根据编码动态获取显示名称

## 技术实现

### 字典 API (`src/api/dict.ts`)

```typescript
// 字典项数据结构
export interface DictOption {
  code: string        // 编码
  name: string        // 名称  
  children?: DictOption[]  // 子级选项
  parentCode?: string      // 父级编码
}

// 核心方法
export const getBusinessTypeName = (businessTypeCode: string): string
export const getChildrenByParentCode = (parentCode: string): DictOption[]
export const getFirstLevelOptions = (): DictOption[]
```

### 业态数据结构

```typescript
// 本地默认数据
export const PROPERTY_TYPE_OPTIONS: DictOption[] = [
  {
    code: 'dormitory',
    name: '宿舍',
    children: [
      { code: 'dormitory_standard', name: '标准宿舍' },
      { code: 'dormitory_apartment', name: '公寓式宿舍' },
      { code: 'dormitory_single', name: '单人宿舍' },
      { code: 'dormitory_double', name: '双人宿舍' }
    ]
  },
  {
    code: 'office', 
    name: '办公',
    children: [
      { code: 'office_standard', name: '标准办公' },
      { code: 'office_executive', name: '行政办公' },
      { code: 'office_creative', name: '创意办公' },
      { code: 'office_shared', name: '共享办公' }
    ]
  },
  {
    code: 'commercial',
    name: '商业', 
    children: [
      { code: 'commercial_retail', name: '零售商业' },
      { code: 'commercial_restaurant', name: '餐饮商业' },
      { code: 'commercial_service', name: '服务商业' },
      { code: 'commercial_entertainment', name: '娱乐商业' }
    ]
  },
  {
    code: 'warehouse',
    name: '仓储',
    children: [
      { code: 'warehouse_standard', name: '标准仓储' },
      { code: 'warehouse_cold', name: '冷链仓储' },
      { code: 'warehouse_smart', name: '智能仓储' },
      { code: 'warehouse_logistics', name: '物流仓储' }
    ]
  }
]
```

### 组件实现要点

#### 1. 表单数据结构
```typescript
const formData = reactive({
  propertyType: '',        // 向后兼容字段
  propertyTypeCode: '',    // 业态编码（主要存储字段）
  propertyTypeName: '',    // 业态名称（辅助字段）
  // ... 其他字段
})
```

#### 2. 二级联动逻辑
```typescript
// 初始化选择器数据
const initBusinessTypeColumns = () => {
  const firstLevelOptions = getFirstLevelOptions(propertyTypeOptions.value)
  const firstColumn = firstLevelOptions.map(option => ({
    text: option.name,
    value: option.code
  }))
  
  const firstParentCode = firstLevelOptions[0]?.code || ''
  const secondLevelOptions = getChildrenByParentCode(firstParentCode, propertyTypeOptions.value)
  const secondColumn = secondLevelOptions.map(option => ({
    text: option.name, 
    value: option.code
  }))
  
  businessTypeColumns.value = [firstColumn, secondColumn]
}

// 监听一级分类变化
const onBusinessTypeChange = (value: any, index: number) => {
  if (index === 0) {
    const parentCode = value
    const secondLevelOptions = getChildrenByParentCode(parentCode, propertyTypeOptions.value)
    const secondColumn = secondLevelOptions.map(option => ({
      text: option.name,
      value: option.code
    }))
    
    businessTypeColumns.value = [
      businessTypeColumns.value[0],
      secondColumn
    ]
  }
}
```

#### 3. 选择确认处理
```typescript
const onBusinessTypeConfirm = (value: any) => {
  const selectedValues = value.selectedValues
  const selectedOptions = value.selectedOptions
  
  if (selectedValues.length >= 2) {
    // 二级选择
    formData.propertyTypeCode = selectedValues[1]
    formData.propertyTypeName = selectedOptions[1].text
    formData.propertyType = selectedValues[1] // 向后兼容
  } else if (selectedValues.length === 1) {
    // 仅选择一级
    formData.propertyTypeCode = selectedValues[0]
    formData.propertyTypeName = selectedOptions[0].text
    formData.propertyType = selectedValues[0]
  }
  
  showBusinessTypePicker.value = false
}
```

#### 4. 显示名称计算
```typescript
// 计算属性：根据编码获取显示名称
const propertyTypeDisplayName = computed(() => {
  return getBusinessTypeName(formData.propertyTypeCode, propertyTypeOptions.value)
})
```

## 使用说明

### 1. 用户操作流程
1. 点击"意向业态"字段
2. 在弹出的选择器中先选择一级分类（如"办公"）
3. 二级分类自动更新为对应的子分类
4. 选择具体的二级分类（如"标准办公"）
5. 点击确认，表单显示完整名称

### 2. 数据流程
1. **页面初始化**：调用 `getPropertyTypeData()` 获取字典数据
2. **备用数据**：如果API失败，使用 `PROPERTY_TYPE_OPTIONS` 本地数据
3. **数据转换**：将字典数据转换为picker组件需要的格式
4. **用户选择**：存储编码到 `formData.propertyTypeCode`
5. **显示转换**：通过 `getBusinessTypeName()` 将编码转换为名称显示

### 3. API 集成
- **获取字典**：`getPropertyTypeDict()` - 从后端获取完整字典数据
- **按类型获取**：`getDictByType(dictType)` - 获取指定类型字典
- **按父级获取**：`getDictByParentCode(parentCode)` - 获取子级字典

## 扩展功能

### 1. 添加新的业态类型
在 `PROPERTY_TYPE_OPTIONS` 中添加新的一级分类：
```typescript
{
  code: 'new_type',
  name: '新业态',
  children: [
    { code: 'new_type_sub1', name: '子类型1', parentCode: 'new_type' },
    { code: 'new_type_sub2', name: '子类型2', parentCode: 'new_type' }
  ]
}
```

### 2. 自定义验证
```typescript
const validatePropertyType = () => {
  if (!formData.propertyTypeCode) {
    showToast('请选择意向业态')
    return false
  }
  
  // 检查是否为有效的二级分类
  const isValidSecondLevel = propertyTypeOptions.value.some(parent => 
    parent.children?.some(child => child.code === formData.propertyTypeCode)
  )
  
  if (!isValidSecondLevel) {
    showToast('请选择具体的业态类型')
    return false
  }
  
  return true
}
```

### 3. 动态加载子级
```typescript
const loadChildrenOptions = async (parentCode: string) => {
  try {
    const res = await getDictByParentCode(parentCode)
    if (res.code === 200) {
      return res.data.map(item => ({
        text: item.name,
        value: item.code
      }))
    }
  } catch (error) {
    console.error('加载子级选项失败:', error)
  }
  return []
}
```

## 注意事项

### 1. 数据一致性
- 确保 `propertyTypeCode` 和 `propertyTypeName` 保持一致
- 提交时主要使用 `propertyTypeCode`
- `propertyType` 字段保留用于向后兼容

### 2. 错误处理
- API 请求失败时自动降级为本地数据
- 选择器数据为空时显示友好提示
- 无效编码时提供默认显示

### 3. 性能优化
- 字典数据在组件挂载时一次性加载
- 计算属性自动缓存名称转换结果
- 避免在选择过程中重复请求API

### 4. 扩展性
- 字典数据结构支持多级嵌套
- API 接口设计支持不同字典类型
- 本地数据可作为默认配置或离线支持

## 测试建议

### 1. 功能测试
- 测试一级分类选择后二级分类的自动更新
- 测试选择不同业态后的显示名称正确性
- 测试API请求失败时的降级处理

### 2. 数据测试
- 验证编码存储的正确性
- 验证名称显示的准确性
- 验证表单提交数据的完整性

### 3. 用户体验测试
- 测试选择器的响应速度
- 测试加载状态的显示
- 测试错误提示的友好性

这个实现完全参考了管理端的设计思路，提供了完整的二级联动功能和基于字典编码的数据管理，同时保持了良好的扩展性和用户体验。 