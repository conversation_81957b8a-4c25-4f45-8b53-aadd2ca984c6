import http from './index'

// 地块信息接口
export interface SysParcel {
    createBy?: string
    createByName?: string
    createTime?: string
    updateBy?: string
    updateByName?: string
    updateTime?: string
    id: string
    mdmParcelId?: string
    projectId?: string
    stageId?: string
    parcelName: string
    mdmNatureName?: string
    landUsage?: number
    mdmAddress?: string
    address?: string
    totalSelfArea?: string
    isDel?: boolean
}

// 楼栋信息接口
export interface SysBuilding {
    createBy?: string
    createByName?: string
    createTime?: string
    updateBy?: string
    updateByName?: string
    updateTime?: string
    id: string
    source?: number
    mdmBuildingId?: string
    projectId?: string
    parcelId?: string
    buildingName: string
    mdmBuildingName?: string
    certificateBuildingName?: string
    upFloorNums?: number
    underFloorNums?: number
    totalSelfArea?: string
    isDel?: boolean
}

// 楼层信息接口
export interface SysFloor {
    createBy?: string
    createByName?: string
    createTime?: string
    updateBy?: string
    updateByName?: string
    updateTime?: string
    id: string
    mdmFloorId?: string
    projectId?: string
    buildingId?: string
    floorName: string
    floorTypeName?: string
    designFloorHeight?: string
    designLoad?: string
    isDel?: boolean
}

/**
 * 查询项目下所有地块信息
 * @param projectId 项目ID
 * @returns 地块列表
 */
export const getParcelList = (projectId: string) => {
    return http.get<SysParcel[]>('/business-rent-rest/project/parcel/list', {
         projectId 
    })
}

/**
 * 查询地块下所有楼栋信息
 * @param parcelId 地块ID
 * @returns 楼栋列表
 */
export const getBuildingSelectList = (parcelId: string) => {
    return http.get<SysBuilding[]>('/business-rent-rest/project/building/selectList', {
        parcelId 
    })
}

/**
 * 查询楼栋下所有楼层信息
 * @param buildingId 楼栋ID
 * @returns 楼层列表
 */
export const getFloorList = (buildingId: string) => {
    return http.get<SysFloor[]>('/business-rent-rest/project/floor/list', {
        buildingId 
    })
} 