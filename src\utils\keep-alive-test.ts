/**
 * Keep-Alive 测试工具
 * 用于验证组件是否被正确缓存
 */

export class KeepAliveTest {
  private static instances = new Map<string, number>()
  
  /**
   * 记录组件实例创建
   * @param componentName 组件名称
   */
  static recordInstance(componentName: string): void {
    const count = this.instances.get(componentName) || 0
    this.instances.set(componentName, count + 1)
    console.log(`[KeepAlive Test] ${componentName} 实例创建次数: ${count + 1}`)
  }
  
  /**
   * 获取组件实例创建次数
   * @param componentName 组件名称
   * @returns 创建次数
   */
  static getInstanceCount(componentName: string): number {
    return this.instances.get(componentName) || 0
  }
  
  /**
   * 重置计数器
   */
  static reset(): void {
    this.instances.clear()
    console.log('[KeepAlive Test] 计数器已重置')
  }
  
  /**
   * 打印所有组件的实例创建情况
   */
  static printReport(): void {
    console.log('[KeepAlive Test] 组件实例创建报告:')
    this.instances.forEach((count, componentName) => {
      const status = count === 1 ? '✅ 正常缓存' : '❌ 可能未缓存'
      console.log(`  ${componentName}: ${count} 次 ${status}`)
    })
  }
}

/**
 * 组件生命周期钩子装饰器
 * 在组件的 onMounted 中调用此函数来记录实例创建
 */
export function useKeepAliveTest(componentName: string) {
  KeepAliveTest.recordInstance(componentName)
  
  return {
    getCount: () => KeepAliveTest.getInstanceCount(componentName),
    printReport: () => KeepAliveTest.printReport()
  }
}
