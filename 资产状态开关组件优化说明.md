# 资产状态开关组件优化说明

## 修改概述
将资产状态展示从 `van-tag` 组件替换为 `van-switch` 组件，提供更直观的状态切换体验。

## 修改内容

### 1. EntryProcess.vue 修改

#### 模板部分
- **原方案**: 使用 `van-tag` 显示"缺失"/"齐全"状态，点击切换
- **新方案**: 使用 `van-switch` 开关控件，直接双向绑定 `asset.isMissing`

```vue
<!-- 原代码 -->
<van-tag 
    :type="asset.isMissing ? 'danger' : 'success'"
    :plain="false"
    size="medium"
    round
    :class="['status-tag', { 'clickable': !isViewMode }]"
    @click="!isViewMode && toggleAssetStatus(roomIndex, assetIndex)"
>
    {{ asset.isMissing ? '缺失' : '齐全' }}
</van-tag>

<!-- 新代码 -->
<div class="switch-container">
    <span class="switch-label">{{ !asset.isMissing ? '齐全' : '缺失' }}</span>
    <van-switch 
        v-model="asset.isMissing"
        :disabled="isViewMode"
        size="20"
        active-color="#ff4444"
        inactive-color="#52c41a"
        @change="() => {}"
    />
</div>
```

#### 脚本部分
- 删除了 `toggleAssetStatus` 方法，因为开关组件自带状态切换功能

### 2. EntryDetail.vue 修改

#### 模板部分
- 同样替换为开关组件，但设置为 `disabled` 状态（只读模式）

```vue
<div class="switch-container">
    <span class="switch-label">{{ !asset.isMissing ? '齐全' : '缺失' }}</span>
    <van-switch 
        v-model="asset.isMissing"
        disabled
        size="20"
        active-color="#ff4444"
        inactive-color="#52c41a"
    />
</div>
```

### 3. 样式优化

#### 新增样式
```less
/* 开关容器样式 */
.switch-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.switch-label {
    font-size: 26px;
    font-weight: 500;
    color: #242433;
    min-width: 60px;
}
```

## 组件配置说明

### van-switch 配置参数
- `v-model="asset.isMissing"`: 双向绑定资产缺失状态
- `size="20"`: 开关大小设置为20px
- `active-color="#ff4444"`: 激活状态（缺失）显示红色
- `inactive-color="#52c41a"`: 非激活状态（齐全）显示绿色
- `disabled`: 在查看模式下禁用开关

### 颜色语义
- **红色** (#ff4444): 表示资产缺失状态
- **绿色** (#52c41a): 表示资产齐全状态

## 用户体验改进

### 1. 更直观的操作
- 开关状态一目了然：左边（绿色）表示齐全，右边（红色）表示缺失
- 无需点击后才知道状态，开关位置直接表明当前状态

### 2. 更好的交互反馈
- 开关切换有流畅的动画效果
- 颜色变化提供即时的视觉反馈
- 禁用状态下开关呈现灰色，明确表示不可操作

### 3. 一致的设计语言
- 符合移动端常见的开关组件设计
- 与其他表单控件保持一致的设计风格

## 技术优势

### 1. 代码简化
- 移除了自定义的 `toggleAssetStatus` 方法
- 直接使用 `v-model` 双向绑定，代码更简洁

### 2. 维护性提升
- 减少了自定义逻辑，降低了维护成本
- 使用成熟的组件库组件，稳定性更高

### 3. 响应式设计
- 开关组件自带响应式处理
- 在不同设备上都有良好的显示效果

## 注意事项

1. **状态逻辑**: `isMissing` 为 `true` 时表示缺失，开关显示为红色激活状态
2. **只读模式**: 在查看详情页面中，开关设置为 `disabled` 状态
3. **标签文字**: 根据 `isMissing` 状态动态显示"齐全"或"缺失"文字

这次优化提升了资产状态管理的用户体验，使操作更加直观和便捷。 