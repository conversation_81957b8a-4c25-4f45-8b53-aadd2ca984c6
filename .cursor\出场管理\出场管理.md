# 出场管理 - 列表
    1. 待办理、办理中、已办理
    2. 筛选条件
        - 天数筛选：3 天，7 天，15 天
        - 输入框：  请输入楼栋/房源/承租方搜索
    3. 列表
        - 字段：合同编号，承租方，退租日期，退租房源数
        - 操作：办理出场
        

## 选择要进场房源
    1. 在营运管理 文件夹 创建 选择要进场房源 组件
    2. 组件内包含
        - 筛选条件
            - 房源名称：房源名称模糊搜索
        - table
            - 字段：多选，房源名称，所属地块，楼栋，楼层
    3. 在进场管理页面 点击 办理进场或者批量自动进场 使用 a-modal 组件 调用 选择要进场房源 组件
    4. 按钮 为 下一步 和 取消，点击下一步 关闭模态框，弹出 办理进场 组件

## 办理进场
    1. 在营运管理 文件夹 创建 ‘办理进场’ 组件
    2. 组件内包含
        - 基本信息（label 和 value 的左右布局， 字段展示）
            - 字段：合同编号，合同用途，租期，承租方
        - 物业交割单 （和 ‘出场结算-物业交割单’ 组件类似）src/views/contractManage/components/checkoutSettlement.vue
            - 使用 a-collapse 组件 包裹 
            - header展示 楼栋名称，进场日期 和 展开按钮
            - 展开后展示字段
                -第一行：进场日期（必填）
                -第二行：标题（房间配套情况）使用 sectionTitle 组件 右边 添加配套 按钮
                    - 添加配套 按钮 点击 新增一条数据
                -第三行：房间配套情况 使用 a-table 组件 展示 房间配套情况
                    - 字段：序号，种类，物品名称，规格，数量
                    - 操作： 多选（缺失）或者 移除
                -第四行：水电度数（只做记录）使用 a-table 组件 展示 sectionTitle
                    - 字段：电表，冷水表，热水表
                -第五行：备注 使用 sectionTitle 组件
                    - textarea
    3. 底部 勾选 给承租方发送进场通知单
    4. 底部 按钮： 确认 和 取消
## 出场办理 组件 （a-modal 组件）
    1. 在营运管理 文件夹 创建 ‘出场办理’ 组件
    2. 组件内包含
        - 第一行：出场提示：（标红）
        - 第二行：1、房源可分批次做出场物业交割，但必须所有退租房源统一结算
        - 第三行：2、物业交割单必须商服和物业全都确认，结算单才能发给客户签字
        - 按钮： 三个按钮 '先物业交割，后续结算'，'物业交割并结算'，'取消'
    3. 点击 先物业交割，后续结算 按钮 进入 办理出场 组件的 第二步
    4. 点击 物业交割并结算 按钮 进入 办理出场 组件的 ‘物业交割和费用结算’ 步骤
## 办理出场 组件 （a-drawer 组件）
    1. 在营运管理 文件夹 创建 ‘办理出场’ 组件
    2. 组件内包含
        - 有三个 tab 页 a-tabs 组件
            - 第一个 tab 页： 退租申请
                - 字段：table 展示 退租类型，退租日期，退租房源，房源数
            - 第二个 tab 页： 物业交割
                - 第一部分内容
                    - 基本信息（label 和 value 的左右布局， 字段展示）
                    - 字段：合同编号，承租方，退租类型，退租日期
                - 第二部分内容
                    - 标题：物业交割单 sectionTitle 组件
                    - 操作行：
                        -全选按钮，输入框，设置出场日期，商服批量确认，复制物业确认单地址
                    - 物业交割单 （和 ‘出场结算-物业交割单’ 组件类似）src/views/contractManage/components/checkoutSettlement.vue
                        - 使用 a-collapse 组件 包裹 
                        - header展示 楼栋名称，商服已确认，物业已确认，出场日期，延迟天数 和 展开按钮
                        - 展开后展示字段
                            - 第一行：进场日期（必填）
                            - 第二行：租控管理，radio 按钮，选择 租控管理
                            - 第三行：标题（房间配套情况）使用 sectionTitle 组件 右边 添加配套 按钮
                                - 添加配套 按钮 点击 新增一条数据
                            - 第四行：房间配套情况 使用 a-table 组件 展示
                                - 字段：序号，种类，物品名称，规格，数量，现状，赔偿金（元），说明
                                - 操作：移除
                                - 现状为 select 组件，选择 正常，损坏，赔偿
                                - 赔偿金为 input number 组件，默认 0
                                - 说明为 input 组件
                            - 第四行：‘房屋其它情况’ sectionTitle
                                - 第一行： ‘门、窗、墙体及其他’ radio 组件，选择 完好 或者 损坏，默认 完好‘，赔偿’ input number 组件，默认 0 ，‘元从押金扣除’
                                - 第二行： ’钥匙交接‘ radio 组件，选择 已交 或者 未交齐，默认 已交’，赔偿’ input number 组件，默认 0 ，‘元从押金扣除’
                                - 第三行： ‘清洁卫生’ radio 组件，选择 自行打扫完毕、洁净 或者 需保洁及垃圾清理费，默认 自行打扫完毕、洁净 ‘，赔偿’ input number 组件，默认 0 ，‘元从押金扣除’
                            - 第五行：水电物业费情况（sectionTitle）
                            - 第六行：a-table
                                - 字段：电表，冷水表，热水表，物业费
                                - 数据为固定两行：1. 读数，2. 欠费
                            - 第五行：房间照片（sectionTitle）
                                - 图片上传
                            - 第七行：固定资产、设备设施评估情况（sectionTitle）
                                - textarea
                            - 第八行：备注（sectionTitle）
                                - textarea
                            - 按钮： 确认，暂存 和 取消
            - 第三个 tab 页： 费用结算
                1. sectionTitle 费用结算，右边操作：查看延迟出场房源，添加费项
                    - table
                        - 字段：序号，收支类型，费用科目，金额，费用周期，费用说明
                        - 操作：新增的 可以 移除
                        - 说明：新增的 都可以编辑 其它情况 可以编辑 费用说明
                        - 底部 费用合计
                    - 其它信息
                        - 独占一行信息：单选 ‘是否减免’， ’减免金额‘ input number 组件 ’元‘， ‘减免原因’ input 组件
                        - 独占一行信息：‘最终费用金额’，数字展示 ’元（应退承租方 金额 元）‘
                2. sectionTitle 承租方收款信息
                    - form
                        - 字段：收款人： input 组件，收款账号： input 组件，开户银行： input 组件
                3. sectionTitle 手续办理情况
                    - form
                        - 字段：营业执照： select 组件，税务登记证： select 组件
                    - 其它信息
                        - 独占一行信息： ’只结算，暂不退款‘，’结算并申请退款‘，单选
            - 还有一种情况是 物业交割和费用结算 一起办理 那就第二步 是 物业交割和费用结算 没有第三步了，物业交割和费用结算就是两块内容的合并
        - 底部 按钮： 确认 和 取消
