# 出场办理页面开发说明

## 概述

基于用户提供的图片需求和参考项目`/Users/<USER>/workspace/wyzgpt/wyzgpt-admin-h5/src/views/operationManage/components/exitHandlerNew.vue`，创建了完整的出场办理页面。

## 主要功能模块

### 1. 基本信息展示
- 合同编号、承租方信息
- 合同期限、创建日期
- 蓝色渐变背景设计，保持与进场办理一致的UI风格

### 2. 房源信息
- 显示项目名称、楼栋、房间号
- 交割日期展示
- 支持多个房源显示

### 3. 出场日期选择
- 必填项，支持日期选择器
- 限制日期范围（2020-2030年）

### 4. 房间配套情况管理
- 动态添加/删除资产
- 资产状态管理（正常/损坏）
- 使用van-switch开关组件展示状态

### 5. 水电度数录入
- 电表读数、电表公摊
- 水表读数、燃气表读数
- 冷表度数、热表度数
- 支持数字输入，带单位显示

### 6. 附件上传
- 支持多文件上传
- 最多9个附件
- 使用vant的van-uploader组件

### 7. 承租方签字
- 必填项，支持手写签名
- 签名画布功能
- 签名预览和重新签名

### 8. 备注信息
- 可选填项
- 支持多行文本输入
- 字数限制200字

## 技术实现

### 1. 页面结构
```
src/views/ExitProcess.vue - 出场办理主页面
```

### 2. 路由配置
```typescript
{
  path: '/exit-process/:id?',
  name: 'ExitProcess',
  component: () => import('../views/ExitProcess.vue'),
  meta: {
    title: '出场办理'
  }
}
```

### 3. API接口扩展
在`src/api/exit.ts`中新增：

#### 数据类型定义
- `ExitProcessDTO` - 出场办理数据类型
- `ExitAssetDTO` - 出场资产数据类型  
- `ExitUtilityDTO` - 出场水电度数类型

#### 接口方法
- `getExitProcessDetail(exitId)` - 获取出场办理详情
- `saveExitProcess(data)` - 保存出场办理信息
- `submitExitProcess(data)` - 提交出场办理

### 4. 数据流程
1. 从出场管理页面点击"办理出场"按钮
2. 调用`chooseExitProcessType`接口选择办理类型
3. 跳转到出场办理页面并传递参数
4. 获取出场详情数据初始化页面
5. 用户填写信息并提交
6. 调用`submitExitProcess`接口完成办理

## 组件依赖

### 1. UI组件
- vant组件库：导航栏、按钮、表单、弹框等
- SignatureCanvas：签名画布组件

### 2. 工具函数
- 日期格式化处理
- 表单验证逻辑
- 文件上传处理

## 样式设计

### 1. 设计理念
- 保持与进场办理页面一致的UI风格
- 移动端友好的响应式设计
- 清晰的信息层级和视觉引导

### 2. 关键样式
- 蓝色渐变背景卡片
- 圆角卡片设计
- 固定底部提交按钮
- 一致的间距和字体规范

## 页面交互

### 1. 出场管理页面集成
修改`ExitManagement.vue`中的`handleExit`方法：
- 先调用选择办理类型接口
- 成功后跳转到出场办理页面
- 传递必要参数（出场单ID、合同信息等）

### 2. 表单验证
- 出场日期必填验证
- 承租方签字必填验证
- 友好的错误提示

### 3. 提交流程
- 确认对话框
- Loading状态反馈
- 成功后自动返回上一页

## 错误处理

### 1. 网络错误
- API调用失败的错误处理
- 用户友好的错误提示
- 降级处理（使用模拟数据）

### 2. 数据验证
- 前端表单验证
- 必填项检查
- 数据格式验证

## 测试要点

### 1. 功能测试
- [ ] 页面正常加载和数据初始化
- [ ] 日期选择器功能
- [ ] 资产添加/删除功能
- [ ] 签名功能
- [ ] 表单提交流程

### 2. 兼容性测试
- [ ] 移动端浏览器兼容性
- [ ] 不同屏幕尺寸适配
- [ ] iOS/Android系统测试

### 3. 异常测试
- [ ] 网络异常情况
- [ ] 数据缺失处理
- [ ] 用户操作异常

## 后续优化

### 1. 性能优化
- 图片懒加载
- 组件按需加载
- 接口请求优化

### 2. 功能增强
- 离线数据缓存
- 表单自动保存
- 更丰富的附件类型支持

### 3. 用户体验
- 更流畅的动画效果
- 更智能的表单填写引导
- 更完善的错误处理

## 文件清单

### 新增文件
- `src/views/ExitProcess.vue` - 出场办理页面

### 修改文件
- `src/router/index.ts` - 添加出场办理路由
- `src/api/exit.ts` - 扩展出场相关API接口
- `src/views/ExitManagement.vue` - 修改办理出场按钮逻辑

## 部署注意事项

1. 确保签名组件`SignatureCanvas`已正确配置
2. 检查文件上传功能的后端支持
3. 验证所有API接口的可用性
4. 测试移动端的触摸交互功能

---

**开发完成时间**: 2024年12月
**开发人员**: AI Assistant
**版本**: v1.0.0 