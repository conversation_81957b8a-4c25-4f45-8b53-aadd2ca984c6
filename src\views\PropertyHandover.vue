<script lang="ts">
export default {
    name: 'PropertyHandover'
}
</script>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
// 这些组件在模板中被自动解析，所以无需显式导入
// import { 
//   NavBar, 
//   Popup, 
//   Cell, 
//   CellGroup, 
//   Icon, 
//   DropdownMenu, 
//   DropdownItem 
// } from 'vant'

const router = useRouter()

// 返回上一页
const onClickLeft = () => {
    router.back()
}

// 项目切换相关
const showProjectPopup = ref(false)
const currentProject = ref('平阳万洋众创城')
const projectList = reactive([
    '平阳万洋众创城',
    '乐清万洋众创城',
    '青田万洋众创城',
    '温州万洋众创城'
])

const selectProject = (project: string) => {
    currentProject.value = project
    showProjectPopup.value = false
}

// 筛选相关
const locationValue = ref('')
const statusValue = ref('')

// 定义交割单数据类型
interface HandoverItem {
    contractNo: string;
    tenant: string;
    exitDate: string;
    houseCount: number;
    synOrFinanceUnchecked: number;
    engineOrServiceUnchecked: number;
}

// 交割单列表数据
const handoverList = reactive<HandoverItem[]>([
    {
        contractNo: 'WYSF--2025-0223',
        tenant: '惠州市拾玖电子商务有限公司',
        exitDate: '2025-04-15',
        houseCount: 8,
        synOrFinanceUnchecked: 3,
        engineOrServiceUnchecked: 0
    },
    {
        contractNo: 'WYSF--2025-0223',
        tenant: '惠州市拾玖电子商务有限公司',
        exitDate: '2025-04-15',
        houseCount: 8,
        synOrFinanceUnchecked: 3,
        engineOrServiceUnchecked: 4
    },
    {
        contractNo: 'WYSF--2025-0223',
        tenant: '惠州市拾玖电子商务有限公司',
        exitDate: '2025-04-15',
        houseCount: 8,
        synOrFinanceUnchecked: 3,
        engineOrServiceUnchecked: 4
    }
])

// 跳转到详情页面
const goToDetail = (item: HandoverItem, tab: string) => {
    // 根据未确认状态决定默认显示哪个页签
    const query: Record<string, string> = {
        contractNo: item.contractNo,
        tenant: item.tenant,
        tab: tab
    }

    router.push({
        name: 'PropertyHandoverDetail',
        query
    })
}
</script>

<template>
    <div class="property-handover">
        <!-- 导航栏 -->
        <van-nav-bar title="物业交割单确认" left-arrow fixed placeholder @click-left="onClickLeft" />

        <!-- 顶部蓝色区域：包含项目选择和筛选 -->
        <div class="blue-header">
            <!-- 项目选择栏 -->
            <div class="project-selector" @click="showProjectPopup = true">
                <div class="project-content">
                    <div class="project-icon">
                        <img src="../assets/images/project-icon.svg" alt="项目图标" />
                    </div>
                    <span class="project-name">{{ currentProject }}</span>
                    <div class="dropdown-circle">
                        <img src="../assets/images/dropdown.svg" alt="下拉图标" class="dropdown-icon" />
                    </div>
                </div>
            </div>

            <!-- 筛选栏 -->
            <div class="filter-bar">
                <div class="filter-item location-filter">
                    <van-dropdown-menu>
                        <van-dropdown-item v-model="locationValue" title="房源位置">
                            <template #title>
                                <div class="filter-item status-filter">
                                    <span class="filter-label">房源位置</span>
                                </div>
                            </template>
                            <van-cell-group>
                                <van-cell title="全部" clickable @click="locationValue = ''">
                                    <template #right-icon>
                                        <van-icon name="success" color="#1677FF" v-if="locationValue === ''" />
                                    </template>
                                </van-cell>
                                <van-cell title="A区" clickable @click="locationValue = 'A区'">
                                    <template #right-icon>
                                        <van-icon name="success" color="#1677FF" v-if="locationValue === 'A区'" />
                                    </template>
                                </van-cell>
                                <van-cell title="B区" clickable @click="locationValue = 'B区'">
                                    <template #right-icon>
                                        <van-icon name="success" color="#1677FF" v-if="locationValue === 'B区'" />
                                    </template>
                                </van-cell>
                            </van-cell-group>
                        </van-dropdown-item>
                    </van-dropdown-menu>
                </div>

                <div class="filter-item location-filter">
                    <van-dropdown-menu>
                        <van-dropdown-item v-model="statusValue" title="确认状态">
                            <template #title>
                                <div class="filter-item status-filter">
                                    <span class="filter-label">确认状态</span>
                                </div>
                            </template>
                            <van-cell-group>
                                <van-cell title="全部" clickable @click="statusValue = ''">
                                    <template #right-icon>
                                        <van-icon name="success" color="#1677FF" v-if="statusValue === ''" />
                                    </template>
                                </van-cell>
                                <van-cell title="未确认" clickable @click="statusValue = '未确认'">
                                    <template #right-icon>
                                        <van-icon name="success" color="#1677FF" v-if="statusValue === '未确认'" />
                                    </template>
                                </van-cell>
                                <van-cell title="已确认" clickable @click="statusValue = '已确认'">
                                    <template #right-icon>
                                        <van-icon name="success" color="#1677FF" v-if="statusValue === '已确认'" />
                                    </template>
                                </van-cell>
                            </van-cell-group>
                        </van-dropdown-item>
                    </van-dropdown-menu>
                </div>
            </div>
        </div>

        <!-- 项目选择弹出层 -->
        <van-popup v-model:show="showProjectPopup" position="bottom" round closeable>
            <div class="project-popup">
                <div class="popup-title">选择项目</div>
                <div class="project-list">
                    <div v-for="(project, index) in projectList" :key="index" class="project-item"
                        :class="{ 'project-item-active': project === currentProject }" @click="selectProject(project)">
                        {{ project }}
                    </div>
                </div>
            </div>
        </van-popup>

        <!-- 内容区域，包含白色圆角背景 -->
        <div class="content-container">
            <!-- 交割单列表 -->
            <div class="handover-list">
                <div class="handover-item" v-for="(item, index) in handoverList" :key="index">
                    <div class="handover-content">
                        <div class="handover-info">
                            <p class="info-item">合同号：{{ item.contractNo }}</p>
                            <p class="info-item">承租方：{{ item.tenant }}</p>
                            <div class="info-row">
                                <p class="info-item">退租日期：{{ item.exitDate }}</p>
                                <p class="info-item">退租房源数：{{ item.houseCount }}间</p>
                            </div>
                        </div>

                        <!-- 未确认状态展示 -->
                        <div class="unchecked-status"
                            v-if="item.synOrFinanceUnchecked > 0 || item.engineOrServiceUnchecked > 0">
                            <div class="status-row">
                                <div class="status-item" v-if="item.synOrFinanceUnchecked > 0"
                                    @click="goToDetail(item, 'finance')">
                                    <span class="status-text">综合或财务未确认</span>
                                    <div class="status-badge">
                                        <span>{{ item.synOrFinanceUnchecked }}</span>
                                    </div>
                                    <img src="../assets/images/path-2.svg" alt="箭头" class="status-arrow" />
                                </div>

                                <!-- 分隔线 -->
                                <div class="divider"
                                    v-if="item.synOrFinanceUnchecked > 0 && item.engineOrServiceUnchecked > 0">
                                </div>

                                <!-- 工程或客服未确认状态 -->
                                <div class="status-item" v-if="item.engineOrServiceUnchecked > 0"
                                    @click="goToDetail(item, 'engineering')">
                                    <span class="status-text">工程或客服未确认</span>
                                    <div class="status-badge">
                                        <span>{{ item.engineOrServiceUnchecked }}</span>
                                    </div>
                                    <img src="../assets/images/path-2-copy.svg" alt="箭头" class="status-arrow" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.property-handover {
    height: 100vh;
    background-color: #F1F1F1;
    display: flex;
    flex-direction: column;
}

/* 顶部蓝色背景区域 */
.blue-header {
    background: linear-gradient(to right, #314FFF, #1677FF);
    padding-bottom: 20px;
}

/* 项目选择器样式 */
.project-selector {
    padding: 20px 32px;
    display: flex;
    align-items: center;
}

.project-content {
    flex: 1;
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.15);
    padding: 10px 30px;
    border-radius: 45px;
    height: 90px;
}

.project-icon {
    margin-right: 15px;
    display: flex;
    align-items: center;
}

.project-icon img {
    width: 38px;
    height: 38px;
}

.project-name {
    flex: 1;
    color: #FFFFFF;
    font-size: 30px;
    font-weight: 600;
    margin-right: 8px;
    line-height: 1.4em;
}

.dropdown-circle {
    width: 40px;
    height: 40px;
    border: 1px solid #FFFFFF;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dropdown-icon {
    height: 20px;
    width: auto;
}

/* 内容容器 */
.content-container {
    position: relative;
    margin-top: -20px;
    background-color: #F1F1F1;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    overflow: hidden;
    padding-bottom: 20px;
    padding-top: 20px;
    flex: 1;
    overflow-y: auto;
}

/* 项目弹出层样式 */
.project-popup {
    padding: 30px;
}

.popup-title {
    font-size: 32px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 30px;
}

.project-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.project-item {
    padding: 20px;
    font-size: 28px;
    border-radius: 8px;
    background-color: #f5f5f5;
}

.project-item-active {
    color: #1677FF;
    font-weight: 500;
    background-color: #ECF5FF;
}

/* 筛选栏样式 */
.filter-bar {
    display: flex;
    justify-content: space-between;
    padding: 0 32px;
    margin-top: 16px;
    margin-bottom: 30px;
}

.filter-item {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.location-filter {
    flex: 1;
    max-width: 45%;
}

.status-filter {
    flex: 1;
    justify-content: flex-end;
}

.filter-label {
    font-size: 26px;
    color: #FFFFFF;
    margin-right: 8px;
    font-weight: 400;
    line-height: 1.4em;
}

.filter-icon {
    width: 12px;
    height: 8px;
    filter: brightness(0) invert(1);
}

/* 交割单列表样式 */
.handover-list {
    padding: 0 32px;
}

.handover-item {
    margin-bottom: 32px;
    background-color: #FFFFFF;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    cursor: pointer;
}

.handover-item:last-child {
    margin-bottom: 0;
}

.handover-content {
    border-radius: 20px;
    overflow: hidden;
}

.handover-info {
    padding: 30px;
}

.info-item {
    font-size: 24px;
    color: #666666;
    margin-bottom: 16px;
    line-height: 1.4;
}

.info-row {
    display: flex;
    justify-content: space-between;
}

.info-row .info-item {
    margin-bottom: 0;
    flex: 1;
}

.info-row .info-item:first-child {
    margin-right: 20px;
}

.unchecked-status {
    background: linear-gradient(to bottom, #ECF5FF, #D5E6FF);
}

.status-row {
    display: flex;
    position: relative;
    height: 90px;
}

.status-item {
    display: flex;
    align-items: center;
    padding: 0;
    flex: 1;
    justify-content: center;
}

.divider {
    width: 1px;
    background-color: rgba(22, 119, 255, 0.2);
    position: absolute;
    left: 50%;
    top: 5%;
    bottom: 5%;
    transform: translateX(-50%);
}

.status-text {
    font-size: 26px;
    color: #1677FF;
    font-weight: 500;
    margin-right: 4px;
    white-space: nowrap;
}

.status-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: #FF9E46;
    border-radius: 18px;
    margin-right: 16px;
}

.status-badge span {
    color: #FFFFFF;
    font-size: 24px;
    font-weight: 500;
}

.status-arrow {
    width: 8px;
    height: 14px;
}

/* 自定义 van-nav-bar 样式 */
:deep(.van-nav-bar .van-icon) {
    color: #333333;
}

/* Vant Dropdown 样式覆盖 */
:deep(.van-dropdown-menu__bar) {
    box-shadow: none;
    background-color: transparent;
    height: auto;
}

:deep(.van-dropdown-menu__item) {
    justify-content: flex-start;
    padding: 0;
}

:deep(.van-dropdown-item__content) {
    max-height: 75vh;
}
</style>