<template>
    <div class="booking-detail-page">
        <!-- 顶部导航 -->
        <van-nav-bar 
            title="定单详情" 
            left-arrow 
            @click-left="$router.go(-1)"
            class="custom-nav-bar"
        />

        <!-- 页面内容 -->
        <div class="page-content">
            <!-- 定单信息区块 -->
            <div class="info-block">
                <moduleTitle title="定单信息" status="" :tag="true"></moduleTitle>
                
                <div class="info-content">
                    <div class="info-item">
                        <span class="info-label">客户姓名：</span>
                        <span class="info-value">{{ orderDetail.customerName }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">意向房源：</span>
                        <span class="info-value">{{ orderDetail.intendedProperty }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">应收定金：</span>
                        <span class="info-value">{{ orderDetail.expectedDeposit }}元</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">已收定金：</span>
                        <span class="info-value">{{ orderDetail.receivedDeposit }}元</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">缴纳日期：</span>
                        <span class="info-value">{{ orderDetail.paymentDate }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">创建日期：</span>
                        <span class="info-value">{{ orderDetail.createDate }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">创建人：</span>
                        <span class="info-value">{{ orderDetail.creator }}</span>
                    </div>
                </div>
            </div>

            <!-- 签约信息区块 -->
            <div class="info-block" v-if="orderDetail.status === 3">
                <moduleTitle title="签约信息" status="" :tag="true"></moduleTitle>
                
                <div class="info-content">
                    <div class="info-item">
                        <span class="info-label">签约客户姓名：</span>
                        <span class="info-value">{{ contractDetail.customerName }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">签约房源：</span>
                        <span class="info-value">{{ contractDetail.property }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">合同周期：</span>
                        <span class="info-value">{{ contractDetail.contractPeriod }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">签约日期：</span>
                        <span class="info-value">{{ contractDetail.signDate }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">签约人：</span>
                        <span class="info-value">{{ contractDetail.creator }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: 'BookingDetail'
}
</script>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import moduleTitle from '@/components/ModuleTitle.vue'
import { getBookingDetail } from '@/api/booking'
import { showToast } from 'vant'


const router = useRouter()
const route = useRoute()

// 定单详情数据
const orderDetail = ref({
    customerName: '',
    intendedProperty: '',
    expectedDeposit: '',
    receivedDeposit: '',
    paymentDate: '',
    createDate: '',
    creator: '',
    status: 0
})

// 签约信息数据
const contractDetail = ref({
    customerName: '',
    property: '',
    contractPeriod: '',
    signDate: '',
    creator: ''
})

// 页面初始化
onMounted(async () => {
    const id = route.params.id || route.query.id
    if (id) {
        await loadOrderDetail(id)
    } else {
        showToast('缺少定单ID')
        router.back()
    }
})

// 加载定单详情
const loadOrderDetail = async (id: string) => {
    try {
        const res = await getBookingDetail(id)
        if (res.data) {
            const { booking, cost } = res.data
            
            // 更新定单信息
            orderDetail.value = {
                customerName: booking.customerName || '',
                intendedProperty: booking.roomName || '',
                expectedDeposit: booking.bookingAmount?.toString() || '0',
                receivedDeposit: booking.receivedAmount?.toString() || '0',
                paymentDate: booking.receivedDate || '',
                createDate: booking.createTime || '',
                creator: booking.createByName || '',
                status: booking.status || ''
            }
            
            // 更新签约信息
            if (booking.contractId) {
                contractDetail.value = {
                    customerName: booking.lesseeName || '',
                    property: booking.roomName || '',
                    contractPeriod: `${booking.startDate || ''}~${booking.endDate || ''}`,
                    signDate: booking.signDate || '',
                    creator: booking.signerName || ''
                }
            }
        }
    } catch (error) {
        console.error('获取定单详情失败:', error)
        showToast('获取定单详情失败')
    }
}
</script>

<style scoped>
.booking-detail-page {
    background-color: #f1f1f1;
    min-height: 100vh;
}

/* 导航栏样式 */
.custom-nav-bar {
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
}

/* 页面内容 */
.page-content {
    box-sizing: border-box;
    padding: 20px;
}

/* 信息区块 */
.info-block {
    background-color: #fff;
    border-radius: 20px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 区块标题栏 */
.block-header {
    background: linear-gradient(135deg, #3583FF 0%, #5CBFFF 100%);
    display: flex;
    align-items: center;
    padding: 19px 26px;
    gap: 20px;
}

.header-indicator {
    background-color: #3583FF;
    border-radius: 16px;
    width: 8px;
    height: 32px;
}

.block-title {
    font-size: 30px;
    font-weight: 500;
    color: #000;
    line-height: 42px;
}

/* 信息内容 */
.info-content {
    padding: 23px 30px 30px 30px;
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 24px;
    line-height: 33px;
}

.info-item:first-child {
    margin-top: 0;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    color: #919199;
    min-width: 140px;
    flex-shrink: 0;
}

.info-value {
    color: #333;
    flex: 1;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .page-content {
        padding: 20px;
    }
    
    .info-content {
        padding: 20px 15px 25px 15px;
    }
    
    .info-item {
        font-size: 28px;
        margin-bottom: 12px;
    }
    
    .info-label {
        min-width: 140px;
    }
    
    .block-title {
        font-size: 32px;
    }
}
</style>