

<template>
    <div class="exit-detail">
        <!-- 导航栏 -->
        <van-nav-bar title="出场详情" left-arrow fixed placeholder @click-left="goBack" />

        <!-- 加载状态 -->
        <van-loading v-if="loading" class="loading-center" size="24px" vertical>
            加载中...
        </van-loading>

        <!-- 内容区域 -->
        <div v-else-if="exitDetail" class="content-area">
            <!-- 基本信息 -->
            <div class="basic-info-section">
                <div class="basic-info-card">
                    <div class="info-content info-content-2">
                        <div class="info-row">
                            <span class="info-label">合同编号：</span>
                            <span class="info-value">{{ basicInfo.contractNo }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">承租方：</span>
                            <span class="info-value">{{ basicInfo.customerName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">租期：</span>
                            <span class="info-value">{{ basicInfo.rentPeriod }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">退租日期：</span>
                            <span class="info-value">{{ basicInfo.terminateDate }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">办理状态：</span>
                            <span class="info-value">{{ basicInfo.progressStatusName }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 出场房源 -->
            <div class="rooms-section">
                <div class="section-title">
                    <img src="../assets/images/house-icon.svg" alt="房源" class="title-icon" />
                    出场房源 · {{ roomList.length }}间
                </div>

                <!-- 房源列表 -->
                <div class="room-list">
                    <div v-for="room in roomList" :key="room.id" class="room-card">
                        <!-- 房间头部 -->
                        <div class="room-header" @click="toggleRoomExpand(room.id)">
                            <div class="room-info">
                                <div class="room-name-row">
                                    <img class="house-icon" src="../assets/images/house-icon.svg" alt="房源" />
                                    <span class="room-name">{{ room.roomName }}</span>
                                </div>
                                <div class="room-date">退租日期：{{ formatDate(room.terminateDate || '') || '未设置' }}</div>
                            </div>
                            <van-icon 
                                :name="expandedRooms.includes(room.id) ? 'arrow-up' : 'arrow-down'" 
                                class="expand-icon" 
                            />
                        </div>

                        <!-- 房间详情 -->
                        <div v-if="expandedRooms.includes(room.id)" class="room-content">
                            <!-- 基本信息 -->
                            <div class="room-basic-info">
                                <div class="info-row">
                                    <span class="info-label">楼栋:</span>
                                    <span class="info-value">{{ room.buildingName || '-' }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">地块:</span>
                                    <span class="info-value">{{ room.parcelName || '-' }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">退租日期:</span>
                                    <span class="info-value">{{ formatDate(room.terminateDate || '') || '未设置' }}</span>
                                </div>
                            </div>

                            <!-- 出场日期和租控管理 -->
                            <div class="date-control-section">
                                <div class="info-row">
                                    <span class="info-label">出场日期:</span>
                                    <span class="info-value">{{ formatDate(room.exitDate || '') || '未设置' }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">租控管理:</span>
                                    <span class="info-value">{{ getRentControlText(room.rentControl) }}</span>
                                </div>
                            </div>

                            <!-- 房间配套情况 -->
                            <div class="assets-section">
                                <div class="section-title-blue">房间配套情况</div>

                                <div v-if="!room.exitRoomAssetsList || room.exitRoomAssetsList.length === 0" class="empty-assets">
                                    暂无配套设施
                                </div>

                                <div v-for="(asset, assetIndex) in room.exitRoomAssetsList" :key="assetIndex" class="asset-item">
                                    <div class="asset-info">
                                        <span class="asset-name">{{ asset.name }}{{ asset.specification ? `(${asset.specification})` : '' }}</span>
                                        <div class="asset-details">
                                            <span class="asset-status" :class="getAssetStatusClass(asset.status)">
                                                {{ getAssetStatusText(asset.status) }}
                                            </span>
                                            <span v-if="asset.penalty" class="asset-penalty">赔偿: ¥{{ asset.penalty }}</span>
                                        </div>
                                    </div>
                                    <div v-if="asset.remark" class="asset-remark">{{ asset.remark }}</div>
                                </div>
                            </div>

                            <!-- 房屋其他清理情况 -->
                            <div class="other-section">
                                <div class="section-title-blue">房屋其他清理情况</div>

                                <div class="other-item">
                                    <span class="other-label">门、窗、墙体及其他:</span>
                                    <span class="other-value" :class="getStatusClass(room.doorWindowStatus)">
                                        {{ getDoorWindowStatusText(room.doorWindowStatus) }}
                                    </span>
                                    <span v-if="room.doorWindowPenalty" class="penalty-amount">赔偿: ¥{{ room.doorWindowPenalty }}</span>
                                </div>

                                <div class="other-item">
                                    <span class="other-label">钥匙交接:</span>
                                    <span class="other-value" :class="getStatusClass(room.keyHandoverStatus)">
                                        {{ getKeyHandoverStatusText(room.keyHandoverStatus) }}
                                    </span>
                                    <span v-if="room.keyPenalty" class="penalty-amount">赔偿: ¥{{ room.keyPenalty }}</span>
                                </div>

                                <div class="other-item">
                                    <span class="other-label">清洁卫生:</span>
                                    <span class="other-value" :class="getStatusClass(room.cleaningStatus)">
                                        {{ getCleaningStatusText(room.cleaningStatus) }}
                                    </span>
                                    <span v-if="room.cleaningPenalty" class="penalty-amount">清理费: ¥{{ room.cleaningPenalty }}</span>
                                </div>
                            </div>

                            <!-- 水电物业费情况 -->
                            <div class="utility-section">
                                <div class="section-title-blue">水电物业费情况</div>

                                <div class="utility-group">
                                    <div class="utility-group-title">抄表读数</div>
                                    <div class="utility-item">
                                        <span class="utility-label">电表:</span>
                                        <span class="utility-value">{{ room.elecMeterReading || 0 }} 度</span>
                                    </div>
                                    <div class="utility-item">
                                        <span class="utility-label">冷水表:</span>
                                        <span class="utility-value">{{ room.coldWaterReading || 0 }} 吨</span>
                                    </div>
                                    <div class="utility-item">
                                        <span class="utility-label">热水表:</span>
                                        <span class="utility-value">{{ room.hotWaterReading || 0 }} 吨</span>
                                    </div>
                                </div>

                                <div class="utility-group">
                                    <div class="utility-group-title">欠费金额</div>
                                    <div class="utility-item">
                                        <span class="utility-label">电费:</span>
                                        <span class="utility-value fee-amount">¥{{ room.elecFee || 0 }}</span>
                                    </div>
                                    <div class="utility-item">
                                        <span class="utility-label">水费:</span>
                                        <span class="utility-value fee-amount">¥{{ room.waterFee || 0 }}</span>
                                    </div>
                                    <div class="utility-item">
                                        <span class="utility-label">物业费:</span>
                                        <span class="utility-value fee-amount">¥{{ room.pmFee || 0 }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 房间照片 -->
                            <div class="photos-section" v-if="getRoomPhotosArray(room).length > 0">
                                <div class="section-title-blue">房间照片</div>
                                <div class="photo-grid">
                                    <div v-for="(photo, photoIndex) in getRoomPhotosArray(room)" :key="photoIndex" class="photo-item">
                                        <img :src="photo.url || photo.content" :alt="`房间照片${photoIndex + 1}`" class="photo-image" @click="previewPhoto(photo.url || photo.content)" />
                                    </div>
                                </div>
                            </div>

                            <!-- 固定资产评估情况 -->
                            <div class="evaluation-section" v-if="room.assetsSituation">
                                <div class="section-title-blue">固定资产、设备设施评估情况</div>
                                <div class="evaluation-content">{{ room.assetsSituation }}</div>
                            </div>

                            <!-- 确认状态 -->
                            <div class="confirm-section">
                                <div class="section-title-blue">确认状态</div>

                                <div class="confirm-item">
                                    <span class="confirm-label">商服确认:</span>
                                    <span class="confirm-value" :class="getConfirmStatusClass(room.isBusinessConfirmed)">
                                        {{ getConfirmStatusText(room.isBusinessConfirmed) }}
                                    </span>
                                    <span v-if="room.businessConfirmTime" class="confirm-time">{{ formatDateTime(room.businessConfirmTime) }}</span>
                                </div>

                                <div class="confirm-item">
                                    <span class="confirm-label">工程确认:</span>
                                    <span class="confirm-value" :class="getConfirmStatusClass(room.isEngineeringConfirmed)">
                                        {{ getConfirmStatusText(room.isEngineeringConfirmed) }}
                                    </span>
                                    <span v-if="room.engineeringConfirmTime" class="confirm-time">{{ formatDateTime(room.engineeringConfirmTime) }}</span>
                                </div>

                                <div class="confirm-item">
                                    <span class="confirm-label">财务确认:</span>
                                    <span class="confirm-value" :class="getConfirmStatusClass(room.isFinanceConfirmed)">
                                        {{ getConfirmStatusText(room.isFinanceConfirmed) }}
                                    </span>
                                    <span v-if="room.financeConfirmTime" class="confirm-time">{{ formatDateTime(room.financeConfirmTime) }}</span>
                                </div>
                            </div>

                            <!-- 备注 -->
                            <div class="remark-section" v-if="room.remark">
                                <div class="section-title-blue">备注</div>
                                <div class="remark-content">{{ room.remark }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 费用明细 -->
            <div class="costs-section" v-if="costList.length > 0">
                <div class="section-title">
                    <img src="../assets/images/settlement-icon.svg" alt="费用" class="title-icon" />
                    费用明细 · {{ costList.length }}项
                </div>

                <div class="cost-list">
                    <div v-for="(cost, costIndex) in costList" :key="costIndex" class="cost-item">
                        <div class="cost-info">
                            <div class="cost-header">
                                <div class="cost-name">{{ cost.subjectName }}</div>
                                <div class="cost-amount" :class="cost.payType === 1 ? 'income' : 'expense'">
                                    {{ cost.payType === 1 ? '+' : '-' }}¥{{ Math.abs(cost.amount) }}
                                </div>
                            </div>
                            <div class="cost-details">
                                <div class="cost-period">{{ cost.startDate }} 至 {{ cost.endDate }}</div>
                                <div v-if="cost.remark" class="cost-remark">{{ cost.remark }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 减免信息 -->
                <div v-if="exitDetail.exitInfo.isDiscount" class="discount-section">
                    <div class="discount-item">
                        <span class="discount-label">减免金额:</span>
                        <span class="discount-amount">¥{{ exitDetail.exitInfo.discountAmount }}</span>
                    </div>
                    <div v-if="exitDetail.exitInfo.discountReason" class="discount-reason">
                        减免原因: {{ exitDetail.exitInfo.discountReason }}
                    </div>
                </div>

                <!-- 最终金额 -->
                <div class="final-amount-section">
                    <div class="final-amount">
                        <span class="label">最终应退:</span>
                        <span class="amount" :class="exitDetail.exitInfo.finalAmount >= 0 ? 'positive' : 'negative'">
                            ¥{{ exitDetail.exitInfo.finalAmount }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- 收款信息 -->
            <div class="payee-section" v-if="exitDetail.exitInfo.finalAmount < 0">
                <div class="section-title">
                    <img src="../assets/images/settlement-icon.svg" alt="收款" class="title-icon" />
                    收款信息
                </div>

                <div class="payee-info">
                    <div class="payee-item">
                        <span class="payee-label">收款人:</span>
                        <span class="payee-value">{{ exitDetail.exitInfo.payeeName || '未设置' }}</span>
                    </div>
                    <div class="payee-item">
                        <span class="payee-label">收款账号:</span>
                        <span class="payee-value">{{ exitDetail.exitInfo.payeeAccount || '未设置' }}</span>
                    </div>
                    <div class="payee-item">
                        <span class="payee-label">开户银行:</span>
                        <span class="payee-value">{{ exitDetail.exitInfo.bankName || '未设置' }}</span>
                    </div>
                </div>
            </div>

            <!-- 手续办理情况 -->
            <div class="procedure-section">
                <div class="section-title">
                    <img src="../assets/images/procedure-icon.svg" alt="手续" class="title-icon" />
                    手续办理情况
                </div>

                <div class="procedure-info">
                    <div class="procedure-item">
                        <span class="procedure-label">营业执照:</span>
                        <span class="procedure-value" :class="getProcedureStatusClass(exitDetail.exitInfo.licenseStatus)">
                            {{ getLicenseStatusText(exitDetail.exitInfo.licenseStatus) }}
                        </span>
                    </div>
                    <div class="procedure-item">
                        <span class="procedure-label">税务登记证:</span>
                        <span class="procedure-value" :class="getProcedureStatusClass(exitDetail.exitInfo.taxCertStatus)">
                            {{ getTaxCertStatusText(exitDetail.exitInfo.taxCertStatus) }}
                        </span>
                    </div>
                    <div class="procedure-item">
                        <span class="procedure-label">退款处理方式:</span>
                        <span class="procedure-value">{{ getRefundProcessTypeText(exitDetail.exitInfo.refundProcessType) }}</span>
                    </div>
                </div>
            </div>

            <!-- 签字方式 -->
            <div class="sign-section">
                <div class="section-title">
                    <img src="../assets/images/procedure-icon.svg" alt="签字" class="title-icon" />
                    签字方式
                </div>

                <div class="sign-info">
                    <div class="sign-item">
                        <span class="sign-label">签字方式:</span>
                        <span class="sign-value">{{ getSignTypeText(exitDetail.exitInfo.signType) }}</span>
                    </div>
                    <div v-if="exitDetail.exitInfo.signTime" class="sign-item">
                        <span class="sign-label">签字时间:</span>
                        <span class="sign-value">{{ formatDateTime(exitDetail.exitInfo.signTime) }}</span>
                    </div>
                    <div v-if="exitDetail.exitInfo.settleTime" class="sign-item">
                        <span class="sign-label">结算时间:</span>
                        <span class="sign-value">{{ formatDateTime(exitDetail.exitInfo.settleTime) }}</span>
                    </div>
                    <div v-if="exitDetail.exitInfo.settleByName" class="sign-item">
                        <span class="sign-label">结算人:</span>
                        <span class="sign-value">{{ exitDetail.exitInfo.settleByName }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <van-empty v-else description="暂无详情数据" />
    </div>
</template>

<script lang="ts">
export default {
  name: 'ExitDetail'
}
</script>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getExitDetail } from '../api/exit'
import { showToast, showLoadingToast } from 'vant'

const router = useRouter()
const route = useRoute()

// 页面数据
const loading = ref(false)
const exitDetail = ref<any>(null)
const basicInfo = ref({
    contractNo: '',
    customerName: '',
    rentPeriod: '',
    terminateDate: '',
    createTime: '',
    createByName: '',
    status: '',
    progressStatusName: ''
})
const roomList = ref<any[]>([])
const costList = ref<any[]>([])
const expandedRooms = ref<string[]>([])

// 获取出场详情
const fetchExitDetail = async () => {
    const exitId = route.query.exitId as string
    if (!exitId) {
        showToast('缺少出场单ID')
        return
    }

    try {
        loading.value = true
        const loadingToast = showLoadingToast({
            message: '加载中...',
            forbidClick: true,
        })

        const response = await getExitDetail(exitId)
        
        if (response.code === 200 && response.data) {
            exitDetail.value = response.data
            setDetailData(response.data)
        } else {
            showToast(response.msg || '获取详情失败')
        }
        
        loadingToast.close()
    } catch (error) {
        console.error('获取出场详情失败:', error)
        showToast('获取详情失败')
    } finally {
        loading.value = false
    }
}

// 设置详情数据
const setDetailData = (data: any) => {
    const exitInfo = data.exitInfo || {}
    const contractInfo = data.contractTerminateInfo?.contract || {}
    
    // 设置基本信息
    basicInfo.value = {
        contractNo: contractInfo.contractNo || '',
        customerName: contractInfo.customerName || '',
        rentPeriod: contractInfo.startDate && contractInfo.endDate 
            ? `${contractInfo.startDate} 至 ${contractInfo.endDate}`
            : '',
        terminateDate: data.contractTerminateInfo?.terminateDate || '',
        createTime: formatDateTime(exitInfo.createTime || ''),
        createByName: exitInfo.createByName || '',
        status: getStatusText(exitInfo.progressStatus),
        progressStatusName: getProgressStatusText(exitInfo.progressStatus)
    }
    
    // 设置房源列表
    roomList.value = data.exitRoomList || []
    
    // 设置费用列表
    costList.value = data.exitCostList || []
    
    // 默认展开第一个房间
    if (roomList.value.length > 0) {
        expandedRooms.value = [roomList.value[0].id]
    }
}

// 获取办理进度状态文本
const getProgressStatusText = (status: number) => {
    // 办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废
    switch (status) {
        case 0: return '不可见'
        case 1: return '待办理'
        case 5: return '物业交割' 
        case 10: return '费用结算'
        case 15: return '交割并结算'
        case 20: return '客户签字'
        case 25: return '发起退款'
        case 30: return '已完成'
        case 40: return '已作废'
        default: return '未知'
    }
}

// 获取状态文本
const getStatusText = (progressStatus: number): string => {
    if (progressStatus === 1) return '待办理'
    if (progressStatus >= 5 && progressStatus < 30) return '办理中'
    if (progressStatus === 30) return '已办理'
    if (progressStatus === 40) return '已作废'
    return '未知'
}

// 格式化日期时间
const formatDateTime = (dateStr: string): string => {
    if (!dateStr) return ''
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    })
}

// 格式化日期
const formatDate = (dateStr: string): string => {
    if (!dateStr) return ''
    return dateStr.split('T')[0]
}

// 切换房间展开状态
const toggleRoomExpand = (roomId: string) => {
    const index = expandedRooms.value.indexOf(roomId)
    if (index > -1) {
        expandedRooms.value.splice(index, 1)
    } else {
        expandedRooms.value.push(roomId)
    }
}

// 获取确认状态文本
const getConfirmStatusText = (isConfirmed: boolean): string => {
    return isConfirmed ? '已确认' : '待确认'
}

// 获取确认状态类名
const getConfirmStatusClass = (isConfirmed: boolean): string => {
    return isConfirmed ? 'confirmed' : 'pending'
}

// 获取租控管理文本
const getRentControlText = (rentControl: number): string => {
    switch (rentControl) {
        case 0: return '不租控'
        case 1: return '租控'
        default: return '未设置'
    }
}

// 获取资产状态文本
const getAssetStatusText = (status: number): string => {
    switch (status) {
        case 1: return '正常'
        case 2: return '损坏'
        case 3: return '缺失'
        default: return '未知'
    }
}

// 获取资产状态类名
const getAssetStatusClass = (status: number): string => {
    switch (status) {
        case 1: return 'normal'
        case 2: return 'damaged'
        case 3: return 'missing'
        default: return ''
    }
}

// 获取门窗状态文本
const getDoorWindowStatusText = (status: number): string => {
    switch (status) {
        case 1: return '正常'
        case 2: return '损坏'
        default: return '未设置'
    }
}

// 获取钥匙交接状态文本
const getKeyHandoverStatusText = (status: number): string => {
    switch (status) {
        case 1: return '已交接'
        case 2: return '未交接'
        default: return '未设置'
    }
}

// 获取清洁状态文本
const getCleaningStatusText = (status: number): string => {
    switch (status) {
        case 1: return '已清洁'
        case 2: return '未清洁'
        default: return '未设置'
    }
}

// 获取通用状态类名
const getStatusClass = (status: number): string => {
    switch (status) {
        case 1: return 'status-normal'
        case 2: return 'status-abnormal'
        default: return ''
    }
}

// 获取房间照片数组
const getRoomPhotosArray = (room: any): any[] => {
    if (!room.roomPhotos) return []
    try {
        return JSON.parse(room.roomPhotos)
    } catch {
        return []
    }
}

// 预览照片
const previewPhoto = (url: string) => {
    // 这里可以实现照片预览功能
    console.log('预览照片:', url)
}

// 获取营业执照状态文本
const getLicenseStatusText = (status: number): string => {
    switch (status) {
        case 0: return '未办理'
        case 1: return '已办理'
        default: return '未设置'
    }
}

// 获取税务登记证状态文本
const getTaxCertStatusText = (status: number): string => {
    switch (status) {
        case 0: return '未办理'
        case 1: return '已办理'
        default: return '未设置'
    }
}

// 获取手续状态类名
const getProcedureStatusClass = (status: number): string => {
    switch (status) {
        case 0: return 'procedure-pending'
        case 1: return 'procedure-completed'
        default: return ''
    }
}

// 获取退款处理方式文本
const getRefundProcessTypeText = (type: number): string => {
    switch (type) {
        case 1: return '银行转账'
        case 2: return '暂存客户账户'
        case 3: return '现金退款'
        default: return '未设置'
    }
}

// 获取签字方式文本
const getSignTypeText = (type: number): string => {
    switch (type) {
        case 1: return '线上签字'
        case 2: return '线下签字'
        default: return '未设置'
    }
}

// 返回上一页
const goBack = () => {
    router.back()
}

// 页面挂载
onMounted(() => {
    fetchExitDetail()
})
</script>

<style scoped>
.exit-detail {
    min-height: 100vh;
    background-color: #F1F1F1;
    position: relative;
    z-index: 1;
}

/* 加载状态 */
.loading-center {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    flex-direction: column;
}

/* 内容区域 */
.content-area {
    padding-bottom: 20px;
}

/* 基本信息区域 */
.basic-info-section {
    margin: 16px;
    overflow: hidden;
}

.basic-info-card {
    background: linear-gradient(135deg, #3583FF 0%, #1677FF 100%); /* 蓝色渐变背景 */
    border-radius: 12px;
    padding: 30px 20px;
    color: #FFFFFF; /* 白色文字 */
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(53, 131, 255, 0.3);
}

.info-content {
    position: relative;
    z-index: 1;
}

.info-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 28px;
    line-height: 1.4;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 400;
    color: #666;
    /* color: rgba(255, 255, 255, 0.9); */
    min-width: 140px;
}

.info-value {
    font-weight: 500;
    color: #333;
    /* color: #FFFFFF; */
    flex: 1;
}

.title-icon {
    width: 32px;
    height: 32px;
}

.section-title {
    font-size: 32px;
    font-weight: 600;
    color: #242433;
    padding: 20px 30px;
    border-bottom: 1px solid #E8EBFF;
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: #FFFFFF;
    border-radius: 12px 12px 0 0;
}

/* 房源区域 */
.rooms-section {
    margin: 0 16px 16px;
}

.room-list {
    margin-top: 16px;
}

.room-card {
    background-color: #FFFFFF;
    border-radius: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

/* 房间头部 */
.room-header {
    display: flex;
    align-items: center;
    padding: 16px;
    cursor: pointer;
    border-bottom: 1px solid #F5F5F5;
}

.room-info {
    flex: 1;
}

.room-name-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.house-icon {
    width: 30px;
    height: 30px;
    margin-right: 16px;
}

.room-name {
    font-size: 28px;
    font-weight: 500;
    color: #242433;
}

.room-date {
    font-size: 24px;
    color: #919199;
}

.expand-icon {
    color: #C8C9CC;
    font-size: 16px;
}

/* 房间内容 */
.room-content {
    padding: 0 16px 16px;
}

/* 房间基本信息 */
.room-basic-info {
    padding: 16px 0;
    border-bottom: 1px solid #F5F5F5;
    margin-bottom: 16px;
}

/* 蓝色标题样式 */
.section-title-blue {
    font-size: 28px;
    font-weight: 500;
    color: #1677FF;
    padding: 22px 0 22px 16px;
    border-bottom: 1px solid #E8EBFF;
}

/* 确认状态区域 */
.confirm-section {
    margin-bottom: 16px;
}

.confirm-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    margin-bottom: 8px;
    border-radius: 8px;
}

.confirm-item:last-child {
    margin-bottom: 0;
}

.confirm-label {
    font-size: 28px;
    color: #666;
}

.confirm-value {
    font-size: 28px;
    font-weight: 500;
}

.confirm-value.confirmed {
    color: #52c41a;
}

.confirm-value.pending {
    color: #ff4d4f;
}

/* 备注区域 */
.remark-section {
    margin-bottom: 16px;
}

.remark-content {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    font-size: 28px;
    color: #333;
    line-height: 1.5;
}

/* 日期控制区域 */
.date-control-section {
    padding: 16px 0;
    border-bottom: 1px solid #F5F5F5;
    margin-bottom: 16px;
}

/* 资产区域 */
.assets-section {
    margin-bottom: 16px;
}

.empty-assets {
    text-align: center;
    color: #C8C9CC;
    font-size: 24px;
    padding: 20px 0;
}

.asset-item {
    padding: 16px 0;
    border-bottom: 1px solid #F5F5F5;
}

.asset-item:last-child {
    border-bottom: none;
}

.asset-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.asset-name {
    font-size: 28px;
    color: #242433;
    flex: 1;
}

.asset-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.asset-status {
    font-size: 24px;
    font-weight: 500;
}

.asset-status.normal {
    color: #52c41a;
}

.asset-status.damaged {
    color: #ff4d4f;
}

.asset-status.missing {
    color: #ff4d4f;
}

.asset-penalty {
    font-size: 22px;
    color: #ff6b35;
}

.asset-remark {
    font-size: 24px;
    color: #666;
    margin-top: 8px;
    padding-left: 16px;
    border-left: 3px solid #f0f0f0;
}

/* 房屋其他情况 */
.other-section {
    margin-bottom: 16px;
}

.other-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    margin-bottom: 8px;
    border-radius: 8px;
    flex-wrap: wrap;
    gap: 8px;
}

.other-item:last-child {
    margin-bottom: 0;
}

.other-label {
    font-size: 28px;
    color: #666;
    min-width: 140px;
}

.other-value {
    font-size: 28px;
    font-weight: 500;
}

.other-value.status-normal {
    color: #52c41a;
}

.other-value.status-abnormal {
    color: #ff4d4f;
}

.penalty-amount {
    font-size: 24px;
    color: #ff6b35;
    margin-left: 8px;
}

/* 水电物业费区域 */
.utility-section {
    margin-bottom: 16px;
}

.utility-group {
    margin-bottom: 16px;
}

.utility-group:last-child {
    margin-bottom: 0;
}

.utility-group-title {
    font-size: 26px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
    padding-left: 16px;
}

.utility-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    margin-bottom: 8px;
    border-radius: 8px;
}

.utility-item:last-child {
    margin-bottom: 0;
}

.utility-label {
    font-size: 28px;
    color: #666;
}

.utility-value {
    font-size: 28px;
    color: #333;
    font-weight: 500;
}

.utility-value.fee-amount {
    color: #ff6b35;
}

/* 照片区域 */
.photos-section {
    margin-bottom: 16px;
}

.photo-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    padding: 16px;
}

.photo-item {
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
}

.photo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
}

/* 评估情况 */
.evaluation-section {
    margin-bottom: 16px;
}

.evaluation-content {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    font-size: 28px;
    color: #333;
    line-height: 1.5;
}

/* 确认状态 */
.confirm-section {
    margin-bottom: 16px;
}

.confirm-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 12px 16px;
    background-color: #f8f9fa;
    margin-bottom: 8px;
    border-radius: 8px;
    flex-wrap: wrap;
    gap: 8px;
}

.confirm-item:last-child {
    margin-bottom: 0;
}

.confirm-label {
    font-size: 28px;
    color: #666;
    min-width: 80px;
}

.confirm-value {
    font-size: 28px;
    font-weight: 500;
}

.confirm-value.confirmed {
    color: #52c41a;
}

.confirm-value.pending {
    color: #ff4d4f;
}

.confirm-time {
    font-size: 24px;
    color: #999;
    width: 100%;
    margin-top: 4px;
}

/* 费用区域 */
.costs-section {
    margin: 0 16px 16px;
}

.cost-list {
    margin-top: 16px;
}

.cost-item {
    background-color: #FFFFFF;
    border-radius: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    padding: 16px;
}

.cost-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.cost-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cost-name {
    font-size: 28px;
    font-weight: 500;
    color: #242433;
}

.cost-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.cost-period {
    font-size: 24px;
    color: #919199;
}

.cost-remark {
    font-size: 24px;
    color: #666;
}

.cost-amount {
    font-size: 28px;
    font-weight: 600;
}

.cost-amount.income {
    color: #52c41a;
}

.cost-amount.expense {
    color: #ff4d4f;
}

/* 减免信息 */
.discount-section {
    margin-top: 16px;
    padding: 16px;
    background-color: #fff7e6;
    border-radius: 8px;
    border: 1px solid #ffd591;
}

.discount-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.discount-label {
    font-size: 28px;
    color: #d46b08;
}

.discount-amount {
    font-size: 28px;
    font-weight: 600;
    color: #d46b08;
}

.discount-reason {
    font-size: 24px;
    color: #d46b08;
}

/* 最终金额 */
.final-amount-section {
    margin-top: 16px;
    padding: 16px;
    background-color: #f6ffed;
    border-radius: 8px;
    border: 1px solid #b7eb8f;
}

.final-amount {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.final-amount .label {
    font-size: 32px;
    font-weight: 600;
    color: #389e0d;
}

.final-amount .amount {
    font-size: 36px;
    font-weight: 700;
}

.final-amount .amount.positive {
    color: #52c41a;
}

.final-amount .amount.negative {
    color: #ff4d4f;
}

/* 收款信息 */
.payee-section {
    margin: 0 16px 16px;
}

.payee-info {
    margin-top: 16px;
}

.payee-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #FFFFFF;
    margin-bottom: 8px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.payee-item:last-child {
    margin-bottom: 0;
}

.payee-label {
    font-size: 28px;
    color: #666;
    min-width: 80px;
}

.payee-value {
    font-size: 28px;
    color: #333;
    font-weight: 500;
    flex: 1;
    text-align: right;
}

/* 手续办理情况 */
.procedure-section {
    margin: 0 16px 16px;
}

.procedure-info {
    margin-top: 16px;
}

.procedure-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #FFFFFF;
    margin-bottom: 8px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.procedure-item:last-child {
    margin-bottom: 0;
}

.procedure-label {
    font-size: 28px;
    color: #666;
    min-width: 120px;
}

.procedure-value {
    font-size: 28px;
    font-weight: 500;
    flex: 1;
    text-align: right;
}

.procedure-value.procedure-pending {
    color: #ff4d4f;
}

.procedure-value.procedure-completed {
    color: #52c41a;
}

/* 签字方式 */
.sign-section {
    margin: 0 16px 16px;
}

.sign-info {
    margin-top: 16px;
}

.sign-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #FFFFFF;
    margin-bottom: 8px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.sign-item:last-child {
    margin-bottom: 0;
}

.sign-label {
    font-size: 28px;
    color: #666;
    min-width: 80px;
}

.sign-value {
    font-size: 28px;
    color: #333;
    font-weight: 500;
    flex: 1;
    text-align: right;
}
.info-content-2 {
    .info-label {
        color: rgba(255, 255, 255, 0.9);
    }
    .info-value {
        color: #fff;
    }
}
.van-icon {
    font-size: 24px;
}
</style>
