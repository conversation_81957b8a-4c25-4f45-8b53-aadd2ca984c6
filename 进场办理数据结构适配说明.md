# 进场办理数据结构适配说明

## 数据结构变化

根据用户提供的 `initEntry` 接口返回数据格式，对进场办理页面的数据处理逻辑进行了适配。

## initEntry 接口返回的数据格式

```json
{
    "contract": {
        "projectName": "福州（马尾）万洋广场",
        "contractPurpose": 32,
        "contractNo": "WYSF-FZ-MWGC-ZHT-2025-0004",
        "customerName": "胡燕芳",
        "startDate": "2025-07-07",
        "endDate": "2029-07-31",
        "id": "bbcb9882861db9edabad6da5e0c4dbdb",
        "projectId": "108",
        "unenterNum": 3
        // ... 其他合同字段
    },
    "customerVo": null,
    "enterRoomList": [
        {
            "id": null,
            "enterId": null,
            "roomId": "19400",
            "roomName": "2A-12",
            "propertyType": 32,
            "parcelName": "/",
            "buildingName": "10幢",
            "enterDate": "2025-07-15",
            "elecMeterReading": null,
            "coldWaterReading": null,
            "hotWaterReading": null,
            "remark": null,
            "assetList": null
        }
    ]
}
```

## 数据处理逻辑适配

### 1. 基本信息设置

**修改前:**
```javascript
basicInfo.value = {
    contractNo: data.contractNo || '',
    contractPurpose: getDictLabel('diversification_purpose', data.contractPurpose?.toString()) || '',
    rentPeriod: data.rentStartDate && data.rentEndDate 
        ? `${data.rentStartDate} 至 ${data.rentEndDate}` 
        : '',
    tenantName: data.tenantName || ''
}
```

**修改后:**
```javascript
const contract = data.contract || data
basicInfo.value = {
    contractNo: contract.contractNo || '',
    contractPurpose: getDictLabel('diversification_purpose', contract.contractPurpose?.toString()) || '',
    rentPeriod: contract.startDate && contract.endDate 
        ? `${contract.startDate} 至 ${contract.endDate}` 
        : '',
    tenantName: contract.customerName || ''
}
```

**关键变化:**
- 数据来源从 `data` 改为 `data.contract`
- 租期字段从 `rentStartDate/rentEndDate` 改为 `startDate/endDate`
- 承租方从 `tenantName` 改为 `customerName`

### 2. 房源列表设置

**修改前:**
```javascript
roomList.value = data.roomList || []
```

**修改后:**
```javascript
roomList.value = data.enterRoomList || data.roomList || []

// 确保每个房间都有必要的字段
roomList.value = roomList.value.map(room => ({
    ...room,
    assetList: room.assetList || []
}))
```

**关键变化:**
- 数据来源从 `roomList` 改为 `enterRoomList`
- 确保每个房间都有 `assetList` 字段，避免空值问题

### 3. 保存数据结构适配

根据 API 定义，增加了更多字段以确保数据完整性：

```javascript
const saveData: EnterAddDTO = {
    id: entryId.value || undefined,
    contractId: contractId.value,
    isNotify: sendNotice.value,
    roomList: roomList.value.map(room => ({
        id: room.id || undefined,
        enterId: room.enterId || undefined,
        roomId: room.roomId,
        roomName: room.roomName,
        propertyType: room.propertyType,
        parcelName: room.parcelName,
        buildingName: room.buildingName,
        enterDate: room.enterDate,
        elecMeterReading: room.elecMeterReading,
        coldWaterReading: room.coldWaterReading,
        hotWaterReading: room.hotWaterReading,
        remark: room.remark,
        assetList: room.assetList?.map(asset => ({
            id: asset.id || undefined,
            enterRoomId: asset.enterRoomId || undefined,
            category: asset.category,
            name: asset.name,
            specification: asset.specification,
            count: asset.count,
            isMissing: asset.isMissing,
            isAdd: asset.isAdd,
            isDel: asset.isDel || false
        })) || [],
        isDel: room.isDel || false
    }))
}
```

## 路由参数优化

### 进场管理页面传递参数

```javascript
router.push({
    name: 'EntryProcess',
    query: {
        contractId: currentEntryItem.value!.contractId,
        roomIds: selectedRooms.value.join(','), // 新增：传递选中的房间ID
        contractNo: currentEntryItem.value!.contractNo,
        tenantName: currentEntryItem.value!.tenantName
    },
    state: {
        entryData: res.data // 通过路由状态传递详情数据
    }
})
```

### 进场办理页面接收参数

支持两种数据来源：

1. **优先使用路由状态数据**（从 initEntry 返回）
2. **备用方案：使用查询参数构造基本数据**

```javascript
// 优先使用路由状态中传递的详情数据（从initEntry接口返回）
const entryData = history.state?.entryData
if (entryData) {
    console.log('使用initEntry返回的详情数据:', entryData)
    setEntryDetailData(entryData)
} else {
    // 备用方案：使用查询参数构造基本信息
    console.log('未找到initEntry数据，使用查询参数构造基本信息')
    // ... 构造基本数据
}
```

## 类型安全改进

修复了 TypeScript 类型错误：

```javascript
// 修改前（会报类型错误）
id: null,
propertyType: null,

// 修改后（类型安全）
id: undefined,
propertyType: undefined,
```

## 调试信息

添加了详细的控制台日志，便于开发调试：

```javascript
console.log('原始数据:', data)
console.log('处理后的房源列表:', roomList.value)
console.log('保存数据:', saveData)
console.log('initEntry返回数据:', res)
```

## 兼容性保障

保持了向后兼容性：

1. **数据来源优先级**: `data.contract || data`
2. **房源数据优先级**: `data.enterRoomList || data.roomList || []`
3. **字段默认值**: 为所有可能为空的字段提供默认值

## 测试建议

1. **正常流程测试**: 从进场管理列表进入进场办理页面
2. **数据完整性测试**: 验证基本信息和房源信息显示正确
3. **保存功能测试**: 验证数据能正确保存到后端
4. **异常处理测试**: 测试数据异常时的降级处理

通过这些适配，进场办理页面能够正确处理 `initEntry` 接口返回的数据格式，提升了数据处理的准确性和稳定性。 