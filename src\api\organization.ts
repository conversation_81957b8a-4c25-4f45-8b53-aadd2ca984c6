import request from './index'

// 组织树查询参数类型
export interface SysOrgTreeDTO {
  name?: string
  relProjectFlag?: number // 未关联项目标识; 1:未关联  2：已关联
  projectIds?: string[]
  merchantId?: string // 商业公司id
}

// 组织树形结构对象类型
export interface SysOrgTreeVo {
  id: string // 组织ID
  code: string // 组织编码
  fullName: string // 组织全称
  name: string // 组织简称
  orgType: number | null // 组织类型; 1:组织  3：项目（实际数据中可能为null）
  level: number // 组织等级 1：集团 2：区域事业部 3：片区 4：项目
  parentId: string // 上级组织ID
  children: SysOrgTreeVo[] | null // 子组织列表（可能为null）
}

// API响应类型
export interface ApiResponse<T> {
  code: number
  msg: string
  data: T
}

/**
 * 获取组织树
 * @param data 查询参数
 */
export const getOrgTree = (data: SysOrgTreeDTO) => {
  return request.post<ApiResponse<SysOrgTreeVo[]>>('/business-rent-rest/org/tree', data)
} 