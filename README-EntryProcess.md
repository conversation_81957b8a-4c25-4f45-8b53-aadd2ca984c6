# 进场办理页面功能说明

## 页面概述

进场办理页面（EntryProcess.vue）是用于处理租户进场手续的核心页面，允许用户填写进场相关信息、管理房间配套设施、记录水电度数等。

## 主要功能

### 1. 基本信息展示
- 合同编号
- 合同用途
- 租期时间
- 承租方名称

### 2. 房源管理
- 显示进场房源列表
- 可展开/收起房源详情
- 设置每个房源的进场日期

### 3. 房间配套情况
- 查看当前房间的配套设施列表
- 添加新的配套设施
- 标记配套设施缺失状态
- 删除新增的配套设施

### 4. 水电度数记录
- 电表度数录入
- 冷水表度数录入
- 热水表度数录入
- 仅做记录用途，不参与计费

### 5. 备注功能
- 为每个房间添加备注信息
- 支持多行文本输入

### 6. 通知设置
- 可选择是否给承租方发送进场通知单

## 页面组件

### 数据结构

```typescript
// 基本信息
interface BasicInfo {
  contractNo: string        // 合同编号
  contractPurpose: string   // 合同用途
  rentPeriod: string       // 租期
  tenantName: string       // 承租方名称
}

// 房间资产
interface RoomAsset {
  category: number          // 资产分类
  name: string             // 资产名称
  specification: string    // 规格说明
  count: number            // 数量
  isMissing: boolean       // 是否缺失
  isAdd: boolean           // 是否为新增
}

// 房间信息
interface RoomInfo {
  roomId: string           // 房间ID
  roomName: string         // 房间名称
  enterDate: string        // 进场日期
  elecMeterReading: number // 电表度数
  coldWaterReading: number // 冷水表度数
  hotWaterReading: number  // 热水表度数
  remark: string           // 备注
  assetList: RoomAsset[]   // 资产列表
}
```

### 核心方法

#### fetchEntryDetail()
获取进场单详情信息，包括基本信息和房源列表。

#### showAssetSelector(roomIndex)
显示资产选择弹窗，允许用户为指定房间添加配套设施。

#### handleAddSelectedAssets()
将选中的资产添加到当前房间的配套列表中。

#### showDatePicker(roomIndex)
显示日期选择器，设置指定房间的进场日期。

#### handleSave()
验证表单数据并保存进场办理信息，支持发送通知功能。

## 页面路由

```javascript
{
  path: '/entry-process',
  name: 'EntryProcess',
  component: () => import('../views/EntryProcess.vue'),
  meta: {
    title: '进场办理'
  }
}
```

### 路由参数

- `entryId`: 进场单ID（可选）
- `contractId`: 合同ID
- `contractNo`: 合同编号（可选）
- `tenantName`: 承租方名称（可选）

## API接口

### 获取进场单详情
```typescript
getEnterDetail(entryId: string)
```

### 保存进场信息
```typescript
saveEntry(data: EnterAddDTO)
```

## 样式特点

- 采用移动端友好的卡片式布局
- 支持房源详情的展开/收起操作
- 蓝色主题色配色方案
- 底部固定操作按钮

## 使用流程

1. 从进场管理列表进入，携带必要的路由参数
2. 页面自动加载基本信息和房源列表
3. 用户逐个展开房源，设置进场日期
4. 添加或调整房间配套设施
5. 录入水电表度数
6. 填写备注信息
7. 选择是否发送通知
8. 点击提交按钮保存数据

## 数据验证

- 必须选择至少一个房源
- 每个房源必须设置进场日期
- 表单提交前会进行完整性检查

## 错误处理

- 网络请求失败时显示错误提示
- 表单验证失败时给出具体提示信息
- 支持操作确认对话框防止误操作

## 注意事项

1. 水电度数仅做记录，不参与费用计算
2. 新增的配套设施可以删除，原有配套只能标记缺失
3. 进场日期设置后会在房源头部显示
4. 保存时会根据发送通知选项决定是否通知承租方 