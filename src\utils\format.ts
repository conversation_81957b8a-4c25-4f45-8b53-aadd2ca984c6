/**
 * 格式化金额
 * @param amount 金额
 * @param precision 精度，默认2位小数
 * @returns 格式化后的金额字符串
 */
export function formatMoney(amount: number | string, precision: number = 2): string {
  if (amount === null || amount === undefined || amount === '') {
    return '0.00'
  }
  
  const num = Number(amount)
  if (isNaN(num)) {
    return '0.00'
  }
  
  return num.toFixed(precision).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * 格式化日期
 * @param date 日期字符串或Date对象
 * @param format 格式，默认 YYYY-MM-DD
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: string | Date | null | undefined, format: string = 'YYYY-MM-DD'): string {
  if (!date) {
    return ''
  }
  
  let dateObj: Date
  if (typeof date === 'string') {
    dateObj = new Date(date)
  } else {
    dateObj = date
  }
  
  if (isNaN(dateObj.getTime())) {
    return ''
  }
  
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hours = String(dateObj.getHours()).padStart(2, '0')
  const minutes = String(dateObj.getMinutes()).padStart(2, '0')
  const seconds = String(dateObj.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化日期时间
 * @param date 日期字符串或Date对象
 * @returns 格式化后的日期时间字符串 YYYY-MM-DD HH:mm:ss
 */
export function formatDateTime(date: string | Date | null | undefined): string {
  return formatDate(date, 'YYYY-MM-DD HH:mm:ss')
}

/**
 * 格式化时间
 * @param date 日期字符串或Date对象
 * @returns 格式化后的时间字符串 HH:mm:ss
 */
export function formatTime(date: string | Date | null | undefined): string {
  return formatDate(date, 'HH:mm:ss')
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化百分比
 * @param value 数值
 * @param precision 精度，默认2位小数
 * @returns 格式化后的百分比字符串
 */
export function formatPercent(value: number, precision: number = 2): string {
  if (isNaN(value)) {
    return '0%'
  }
  
  return (value * 100).toFixed(precision) + '%'
}

/**
 * 格式化手机号
 * @param phone 手机号
 * @returns 格式化后的手机号 (中间4位用*替代)
 */
export function formatPhone(phone: string): string {
  if (!phone || phone.length !== 11) {
    return phone
  }
  
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 格式化身份证号
 * @param idCard 身份证号
 * @returns 格式化后的身份证号 (中间部分用*替代)
 */
export function formatIdCard(idCard: string): string {
  if (!idCard || idCard.length < 8) {
    return idCard
  }
  
  return idCard.replace(/(\d{4})\d*(\d{4})/, '$1**********$2')
} 