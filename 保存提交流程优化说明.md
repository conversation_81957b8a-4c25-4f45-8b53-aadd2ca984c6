# 保存提交流程优化说明

## 功能概述
优化了BookingCreate.vue中的保存和提交流程，实现了页面留存、按钮状态管理和自动生成收款码的功能。

## 核心业务流程

### 1. 保存流程
**操作**: 点击"保存"按钮
- ✅ 留在当前页面
- ✅ 保存按钮置灰，显示"已保存"
- ✅ 提交按钮变为可用状态
- ✅ 保存接口返回的`msg`字段作为定单ID保存到`formData.id`

### 2. 提交流程
**操作**: 点击"提交"按钮（必须先保存）
- ✅ 留在当前页面
- ✅ 提交按钮置灰，显示"已提交"
- ✅ 定单状态变为"已生效"
- ✅ 自动生成定单收款账单码

## 实现细节

### 1. 状态管理
```javascript
// 按钮状态管理
const isSaved = ref(false) // 是否已保存
const isSubmitted = ref(false) // 是否已提交
const saveLoading = ref(false) // 保存按钮加载状态
const submitLoading = ref(false) // 提交按钮加载状态

// 表单数据增加状态字段
const formData = reactive({
    // ...其他字段
    status: 0 // 定单状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废
})
```

### 2. 按钮状态控制
```vue
<!-- 保存按钮 -->
<van-button 
    class="save-btn" 
    :disabled="isSaved || isSubmitted"
    :loading="saveLoading"
    @click="handleSave">
    {{ isSaved ? '已保存' : '保存' }}
</van-button>

<!-- 提交按钮 -->
<van-button 
    type="primary" 
    class="submit-btn" 
    :disabled="!isSaved || isSubmitted"
    :loading="submitLoading"
    @click="handleSubmit">
    {{ isSubmitted ? '已提交' : '提交' }}
</van-button>
```

### 3. 保存逻辑优化
```javascript
const handleSave = async () => {
    if (!validateForm()) return
    
    try {
        saveLoading.value = true
        
        const params = {
            ...formData,
            isSubmit: 0, // 标记为保存状态
            isRefundable: formData.isRefundable === '是' ? 1 : 0,
            roomName: formData.isUncertainRoom ? '暂不确认房源' : formData.roomName,
            roomId: formData.isUncertainRoom ? '' : formData.roomId
        }
        
        const res = await saveBooking(params)
        if (res.code === 200) {
            // 关键：保存接口返回的msg作为定单ID
            formData.id = res.msg || res.data?.id || formData.id
            formData.status = 0 // 草稿状态
            
            isSaved.value = true
            showToast('保存成功，可以提交了')
        }
    } catch (error) {
        showToast('保存失败，请重试')
    } finally {
        saveLoading.value = false
    }
}
```

### 4. 提交逻辑优化
```javascript
const handleSubmit = async () => {
    if (!validateForm()) return
    
    // 必须先保存才能提交
    if (!isSaved.value || !formData.id) {
        showToast('请先保存定单')
        return
    }
    
    try {
        submitLoading.value = true
        
        const params = {
            ...formData,
            isSubmit: 1, // 标记为提交状态
            // 其他参数处理...
        }
        
        const res = await saveBooking(params)
        if (res.code === 200) {
            formData.status = 1 // 待收费状态
            isSubmitted.value = true
            showToast('提交成功，定单已生效')
            
            // 定单生效后自动生成账单码
            await generateQRCode()
        }
    } catch (error) {
        showToast('提交失败，请重试')
    } finally {
        submitLoading.value = false
    }
}
```

### 5. 收款码生成优化
```javascript
const generateQRCode = async () => {
    if (!formData.id || !formData.bookingAmount) {
        console.warn('生成收款码所需参数不完整')
        return
    }
    
    try {
        const loadingToast = showLoadingToast('生成收款码中...')
        
        const res = await payBooking({
            bookingId: formData.id,
            amount: Number(formData.bookingAmount)
        })
        
        loadingToast.close()
        
        if (res.code === 200 && res.data?.paymentUrl) {
            paymentUrl.value = res.data.paymentUrl
            showToast('收款码生成成功')
        }
    } catch (error) {
        showToast('收款码生成失败，请重试')
    }
}
```

## 收款码区域实现

### 1. 显示条件
```vue
<!-- 收款码区域 - 只在提交后显示 -->
<div class="qrcode-section" v-if="isSubmitted && formData.status === 1">
```

### 2. 定单信息展示
```vue
<!-- 定单信息 -->
<div class="order-info" v-if="paymentUrl">
    <div class="info-item">
        <span class="label">项目名称：</span>
        <span class="value">{{ formData.projectName }}</span>
    </div>
    <div class="info-item">
        <span class="label">意向房源：</span>
        <span class="value">{{ formData.isUncertainRoom ? '暂不确认房源' : formData.roomName }}</span>
    </div>
    <div class="info-item">
        <span class="label">客户名称：</span>
        <span class="value">{{ formData.customerName }}</span>
    </div>
    <div class="info-item">
        <span class="label">支付金额：</span>
        <span class="value amount">¥{{ formatAmount(formData.bookingAmount) }}</span>
    </div>
</div>
```

### 3. 二维码组件
```vue
<QRCode 
    :value="paymentUrl" 
    :size="200" 
    :show-placeholder="true" 
    :show-download="!!paymentUrl"
    placeholder-text="收款码已自动生成" 
    @generated="handleQRCodeGenerated" 
    @error="handleQRCodeError" />
```

### 4. 重新生成功能
```vue
<!-- 重新生成按钮 -->
<div class="qrcode-actions" v-if="paymentUrl">
    <van-button 
        type="default" 
        size="small" 
        @click="generateQRCode">
        重新生成收款码
    </van-button>
</div>
```

## 用户交互流程

### 正常流程
1. **填写表单** → 验证表单数据
2. **点击保存** → 调用保存接口 → 保存按钮置灰 → 提交按钮可用
3. **点击提交** → 调用提交接口 → 提交按钮置灰 → 定单生效
4. **自动生成收款码** → 显示收款码区域 → 可重新生成

### 异常处理
- **未保存直接提交**：提示"请先保存定单"
- **保存失败**：显示错误信息，按钮恢复可用状态
- **提交失败**：显示错误信息，按钮恢复可用状态
- **收款码生成失败**：显示错误信息，可重新生成

## 按钮状态矩阵

| 状态 | 保存按钮 | 提交按钮 | 说明 |
|------|----------|----------|------|
| 初始状态 | 可用 | 禁用 | 未保存时不能提交 |
| 保存中 | 加载中 | 禁用 | 正在保存 |
| 已保存 | 已保存(禁用) | 可用 | 可以提交 |
| 提交中 | 已保存(禁用) | 加载中 | 正在提交 |
| 已提交 | 已保存(禁用) | 已提交(禁用) | 流程完成 |

## 数据流转

### 保存阶段
```
表单数据 → 保存接口 → 返回定单ID → 更新formData.id → 按钮状态变更
```

### 提交阶段
```
已保存的定单 → 提交接口 → 定单生效 → 生成收款码 → 显示收款码区域
```

## 技术要点

### 1. 接口数据处理
- 保存接口的`res.msg`字段包含定单ID
- 提交接口使用相同的保存接口，通过`isSubmit`参数区分
- 收款码接口需要定单ID和金额参数

### 2. 状态同步
- 按钮状态与接口调用状态同步
- 表单数据与后端返回数据同步
- UI显示与业务状态同步

### 3. 用户体验优化
- 加载状态提示
- 操作成功/失败反馈
- 按钮状态清晰表达当前可执行的操作
- 页面留存避免用户丢失已填写的数据

## 测试要点

### 功能测试
1. ✅ 保存功能正常，按钮状态正确
2. ✅ 提交功能正常，需先保存
3. ✅ 收款码自动生成
4. ✅ 页面留存，不跳转

### 异常测试
1. ✅ 网络异常时的错误处理
2. ✅ 未保存直接提交的拦截
3. ✅ 收款码生成失败的处理

### 兼容性测试
1. ✅ TypeScript类型检查通过
2. ✅ 构建成功无错误
3. ✅ 与现有API接口兼容

通过这次优化，保存和提交流程更加符合用户预期，提供了更好的操作反馈和状态管理。 