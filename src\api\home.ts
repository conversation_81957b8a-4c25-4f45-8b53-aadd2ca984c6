import request from './index'

// 首页统计数据接口
export interface HomeStatsData {
  // 待办事项统计
  todoStats: {
    pendingBooking: number // 待生效定单
    waitingBooking: number // 待签定单
    waitingEntry: number // 待进场
    waitingExit: number // 待出场
    exitProcessing: number // 出场办理中
  }
  // 房态统计
  roomStats: {
    residential: {
      rented: number // 在租
      vacant: number // 空置
      total: number // 合计
      rentedOrSignedOrReserved: number // 待生效/签约中/已预定
    }
    factory: {
      rented: number
      vacant: number
      total: number
      rentedOrSignedOrReserved: number
    }
    commercial: {
      rented: number
      vacant: number
      total: number
      rentedOrSignedOrReserved: number
    }
  }
}

// 项目信息
export interface ProjectInfo {
  id: string
  name: string
  shortName: string
}

/**
 * 获取首页统计数据
 * @param projectId 项目ID
 */
export const getHomeStats = (projectId?: string) => {
  return request.get<HomeStatsData>('/business-rent-rest/home/<USER>', { projectId })
}

/**
 * 获取用户可访问的项目列表
 */
export const getUserProjects = () => {
  return request.get<ProjectInfo[]>('/business-rent-rest/home/<USER>')
}

/**
 * 获取当前用户的默认项目
 */
export const getCurrentProject = () => {
  return request.get<ProjectInfo>('/business-rent-rest/home/<USER>')
}

/**
 * 设置用户的默认项目
 * @param projectId 项目ID
 */
export const setCurrentProject = (projectId: string) => {
  return request.post('/business-rent-rest/home/<USER>', { projectId })
}

// getDingTalkToken  http://172.30.1.254:8580/auth/getDingTalkToken?code=7b7a3eb3118439be8df0ec7dc8120d74
export const getDingTalkToken = (code: string) => {
  return request.get(`/auth/getDingTalkToken?code=${code}`)
}

//获取房态统计数据
export const getRoomStats = (projectId: string) => {
  return request.get(`/business-rent-rest/homePage/room?projectId=${projectId}`)
}

//获取首页待办数据
export const getPendingStats = (projectId: string) => {
  return request.get(`/business-rent-rest/homePage/pending?projectId=${projectId}`)
}