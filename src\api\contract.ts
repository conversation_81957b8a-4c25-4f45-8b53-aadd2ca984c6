import http from './index';

// 合同支付参数
export interface ContractPaymentDto {
  contractId: string;
  amount: number;
  costList: ContractPaymentItemDto[];
}

// 合同支付明细
export interface ContractPaymentItemDto {
  costId: string;
  payAmount: number;
}

// 合同详情响应
export interface ContractDetailVo {
  contract: ContractVo;
  unpaidBills: CostVo[];
}

// 合同基本信息
export interface ContractVo {
  id: string;
  projectId: string;
  projectName: string;
  contractNo: string;
  customerName: string;
  roomName: string;
  startDate: string;
  endDate: string;
  customer: ContractCustomerVo;
  rooms: ContractRoomVo[];
}

// 合同客户信息
export interface ContractCustomerVo {
  id: string;
  contractId: string;
  customerId: string;
  customerType: number;
  customerName: string;
  phone: string;
  address: string;
}

// 合同房源信息
export interface ContractRoomVo {
  id: string;
  contractId: string;
  roomId: string;
  roomName: string;
  buildingName: string;
  floorName: string;
  area: number;
}

// 账单信息（与booking.ts中的CostVo保持一致）
export interface CostVo {
  id: string;
  projectId: string;
  projectName: string;
  chargeType: number;
  bizId: string;
  bizNo: string;
  customerId: string;
  customerName: string;
  costType: number;
  period: number;
  subjectId: string;
  subjectName: string;
  startDate: string;
  endDate: string;
  receivableDate: string;
  status: number;
  canPay: boolean;
  isRevenueBill: boolean;
  isRevenueGenerated: boolean;
  totalAmount: number;
  discountAmount: number;
  actualReceivable: number;
  receivedAmount: number;
  carryoverAmount: number;
  confirmStatus: number;
  createByName: string;
  updateByName?: string;
  isDel: boolean;
  contractType: number;
  roomName: string;
  contractStatus: number;
  contractStatusTwo: number;
}

// 支付响应
export interface PaymentResult {
  code: number;
  msg: string;
  paymentUrl?: string;
}

/**
 * 获取合同详情
 * 根据合同ID查询合同基础信息和未收齐账单信息
 */
export function getContractDetail(id: string) {
  return http.get<ContractDetailVo>('/business-rent-rest/contract/detail', { id });
}

/**
 * 合同支付接口
 * 根据合同ID和支付明细生成支付链接
 */
export function payContract(data: ContractPaymentDto) {
  return http.post<PaymentResult>('/business-rent-rest/contract/pay', data);
}

// 账单类型枚举
export const COST_TYPE = {
  BOND: 1,     // 保证金
  RENT: 2,     // 租金
  OTHER: 3     // 其他费用
} as const;

// 账单状态枚举
export const COST_STATUS = {
  PENDING_RECEIVE: 0,  // 待收
  PENDING_PAY: 1,      // 待付
  RECEIVED: 2,         // 已收
  PAID: 3              // 已付
} as const;

// 账单状态文本映射
export const COST_STATUS_TEXT = {
  [COST_STATUS.PENDING_RECEIVE]: '待收',
  [COST_STATUS.PENDING_PAY]: '待付',
  [COST_STATUS.RECEIVED]: '已收',
  [COST_STATUS.PAID]: '已付'
} as const;

// 账单类型文本映射
export const COST_TYPE_TEXT = {
  [COST_TYPE.BOND]: '保证金',
  [COST_TYPE.RENT]: '租金',
  [COST_TYPE.OTHER]: '其他费用'
} as const; 