<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .demo-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .demo-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px 0;
        }
        .demo-link:hover {
            background: #0056b3;
        }
        code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .token-example {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>万洋资管平台 - 登录功能演示</h1>
    
    <div class="demo-section">
        <h2>功能说明</h2>
        <p>这个登录页面支持以下功能：</p>
        <ul>
            <li><strong>URL参数登录</strong>：通过URL中的 <code>token</code> 参数自动登录</li>
            <li><strong>手动输入登录</strong>：如果没有URL参数，可以手动输入token</li>
            <li><strong>自动跳转</strong>：登录成功后自动跳转到首页</li>
            <li><strong>本地存储</strong>：token会被安全存储在本地浏览器中</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>使用方法</h2>
        <h3>方法1: URL参数登录（推荐）</h3>
        <p>在URL中添加token参数，用户点击链接即可自动登录：</p>
        <div class="token-example">
            http://localhost:5173/login?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.example.token
        </div>
        
        <h3>方法2: 手动输入登录</h3>
        <p>直接访问登录页面，手动输入token：</p>
        <div class="token-example">
            http://localhost:5173/login
        </div>
    </div>

    <div class="demo-section">
        <h2>演示链接</h2>
        <p>点击以下链接测试登录功能：</p>
        
        <h3>示例Token登录链接：</h3>
        <a href="/login?token=demo_token_12345678901234567890" class="demo-link">
            使用示例Token登录
        </a>
        <br>
        <a href="/login?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c" class="demo-link">
            使用JWT Token登录
        </a>
        
        <h3>手动输入登录：</h3>
        <a href="/login" class="demo-link">
            手动输入Token登录
        </a>
    </div>

    <div class="demo-section">
        <h2>测试Token</h2>
        <p>用于测试的示例token（复制以下任意一个用于测试）：</p>
        
        <h4>简单Token:</h4>
        <div class="token-example">demo_token_12345678901234567890</div>
        
        <h4>JWT Token:</h4>
        <div class="token-example">eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c</div>
        
        <h4>长Token:</h4>
        <div class="token-example">wyzgpt_access_token_1234567890abcdefghijklmnopqrstuvwxyz0987654321</div>
    </div>

    <div class="demo-section">
        <h2>技术实现</h2>
        <p>登录页面的主要特性：</p>
        <ul>
            <li><strong>Vue 3 Composition API</strong>：使用最新的Vue 3语法</li>
            <li><strong>Vue Router</strong>：路由参数解析和页面跳转</li>
            <li><strong>本地存储</strong>：安全的token存储和管理</li>
            <li><strong>响应式设计</strong>：支持移动端和桌面端</li>
            <li><strong>错误处理</strong>：完善的错误提示和重试机制</li>
            <li><strong>Loading状态</strong>：优雅的加载动画</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>开发说明</h2>
        <p>如果您是开发者，可以通过以下步骤集成此登录功能：</p>
        <ol>
            <li>确保已安装Vue 3和Vue Router依赖</li>
            <li>将Login.vue组件放在 <code>src/views/</code> 目录下</li>
            <li>在路由配置中添加 <code>/login</code> 路由</li>
            <li>确保 <code>src/utils/auth.ts</code> 包含token管理函数</li>
            <li>根据需要自定义token验证逻辑</li>
        </ol>
    </div>

    <script>
        // 页面加载完成后，更新链接为当前域名
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a[href^="/login"]');
            const currentOrigin = window.location.origin;
            
            links.forEach(link => {
                const href = link.getAttribute('href');
                link.setAttribute('href', currentOrigin + href);
            });
        });
    </script>
</body>
</html> 