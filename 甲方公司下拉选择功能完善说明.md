# 甲方公司下拉选择功能完善说明

## 修改概述
完善了BookingCreate页面中甲方公司字段的下拉选择功能，使其能够根据项目动态加载甲方公司选项，并支持编辑模式下的数据回显。

## 功能特性

### 1. 字段配置
- **字段名称**: 甲方公司
- **存储字段**: 
  - `merchantId`: 甲方公司ID
  - `merchantName`: 甲方公司名称（显示用）
- **数据源**: `merchantOptions` 数组

### 2. 数据格式
merchantOptions的数据格式：
```json
[
    {
        "id": "4ba3e72f1e15c3c40ca982af5c5c28ad",
        "orgCompanyName": "泰顺万睿商业管理有限公司"
    },
    {
        "id": "ace1214df3de2e5d3b83626b07ec476c",
        "orgCompanyName": "浙江万洋众创城商业经营管理有限公司"
    },
    {
        "id": "2a5c60dc154be2597b071f17127c8272",
        "orgCompanyName": "福州万睿商业运营管理有限公司"
    },
    {
        "id": "aac6618b77b0b2d38b20f4ef16019ec3",
        "orgCompanyName": "温州万洋中强资产管理有限公司"
    }
]
```

## 技术实现

### 1. 页面结构
取消了甲方公司字段的注释，启用了完整的选择器功能：
```vue
<van-field v-model="formData.company" required readonly name="company" 
           label="甲方公司" placeholder="请选择" input-align="right"
           @click="showCompanyPicker = true">
    <template #button>
        <i class="van-icon van-icon-arrow-down"></i>
    </template>
</van-field>
<van-popup v-model:show="showCompanyPicker" destroy-on-close position="bottom">
    <van-picker :columns="companyColumns" :model-value="companyValue" 
                @confirm="onCompanyConfirm" @cancel="showCompanyPicker = false" 
                title="选择甲方公司" />
</van-popup>
```

### 2. 数据处理函数

#### updateCompanyColumns()
将merchantOptions数据转换为选择器需要的格式，并自动选择第一个选项：
```javascript
const updateCompanyColumns = () => {
    if (merchantOptions.value && Array.isArray(merchantOptions.value)) {
        companyColumns.value = merchantOptions.value.map((item: any) => ({
            text: item.orgCompanyName,
            value: item.id
        }))
        
        // 如果当前没有选择甲方公司，且选项不为空，默认选择第一个
        if ((!formData.merchantId || !formData.company) && companyColumns.value.length > 0) {
            const firstOption = companyColumns.value[0]
            formData.merchantId = firstOption.value
            formData.company = firstOption.text
            companyValue.value = [0]
        }
    }
}
```

#### setCompanyPickerValue()
设置编辑模式下选择器的当前值：
```javascript
const setCompanyPickerValue = () => {
    if (formData.merchantId && companyColumns.value.length > 0) {
        const index = companyColumns.value.findIndex(item => item.value === formData.merchantId)
        if (index !== -1) {
            companyValue.value = [index]
        }
    }
}
```

### 3. 项目切换联动
当项目发生变化时，自动清空甲方公司选择并重新加载：
```javascript
const onProjectConfirm = async (value: any) => {
    // ... 项目切换逻辑
    if (oldProjectId !== newProjectId) {
        // 清空甲方公司选择
        formData.merchantId = ''
        formData.company = ''
        
        // 获取新项目的甲方公司列表
        await getProjectDetailData(newProjectId)
    }
}
```

### 4. 编辑模式支持
在编辑模式下正确加载和显示甲方公司信息：
```javascript
const loadEditData = async (id: string) => {
    // ... 加载编辑数据
    formData.company = bookingData.company || bookingData.merchantName
    
    // 设置甲方公司选择器的当前值
    setCompanyPickerValue()
}
```

### 5. 表单验证
启用甲方公司字段的必填验证：
```javascript
const validateForm = () => {
    if (!formData.company || !formData.merchantId) {
        showToast('请选择甲方公司')
        return false
    }
    // ... 其他验证
}
```

## API接口

### getProjectDetail(id: string)
获取项目详情，包含该项目的甲方公司列表：
- **接口地址**: `/business-rent-rest/project/detail?id=${id}`
- **返回数据**: `res.data.merchantList` 包含甲方公司数组

## 数据流程

1. **页面初始化**: 根据当前项目ID调用`getProjectDetailData()`获取甲方公司列表
2. **默认选择**: 获取甲方公司列表后，如果当前没有选择，自动选择第一个选项
3. **项目切换**: 用户选择新项目时，清空当前甲方公司选择，重新获取新项目的甲方公司列表并自动选择第一个
4. **编辑模式**: 加载定单详情后，根据保存的`merchantId`设置选择器当前值（优先级高于默认选择）
5. **用户选择**: 用户手动选择甲方公司后，同时保存`merchantId`和`company`（显示名称）

## 用户体验提升

1. **动态加载**: 根据选择的项目动态加载对应的甲方公司选项
2. **数据联动**: 项目切换时自动更新甲方公司选项
3. **默认选择**: 当有多个甲方公司选项时，自动选择第一个作为默认值
4. **编辑回显**: 编辑模式下正确显示已选择的甲方公司
5. **必填验证**: 确保用户必须选择甲方公司才能提交
6. **用户友好**: 清晰的选择器标题和占位符文本

## 注意事项

1. 甲方公司选项依赖于项目选择，用户必须先选择项目
2. 项目切换会清空当前的甲方公司选择
3. 编辑模式下会自动设置选择器的当前值
4. 表单提交时会验证甲方公司字段的完整性

## 测试建议

1. 测试项目选择后甲方公司选项的加载
2. 测试项目切换时甲方公司选择的清空和重新加载
3. 测试编辑模式下甲方公司的正确回显
4. 测试甲方公司必填验证的有效性
5. 测试不同项目下甲方公司选项的正确性