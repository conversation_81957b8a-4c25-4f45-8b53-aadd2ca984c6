<template>
	<div class="signature-canvas">
		<!-- 签字区域 -->
		<div class="signature-container">
			<canvas ref="canvasRef" class="signature-board" @touchstart="startDraw" @touchmove="drawing"
				@touchend="endDraw" @mousedown="startDraw" @mousemove="drawing" @mouseup="endDraw"
				@mouseleave="endDraw"></canvas>

			<!-- 提示文字 -->
			<div class="signature-placeholder" v-if="isEmpty">
				<span>请在此区域签字</span>
			</div>
		</div>

		<!-- 操作按钮 -->
		<div class="signature-actions">
			<van-button class="clear-btn" type="default" @click="clearSignature" :disabled="isEmpty">
				清除
			</van-button>
			<van-button class="confirm-btn" type="primary" @click="confirmSignature" :disabled="isEmpty || uploading"
				:loading="uploading">
				{{ uploading ? '上传中...' : '确认签字' }}
			</van-button>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { showToast } from 'vant'
import { uploadImage, type UploadResponse } from '../api/exit'
import { compressImage } from '../utils/upload'

interface SignatureEmits {
	(e: 'confirm', uploadResponse: UploadResponse): void
	(e: 'cancel'): void 
}

const emit = defineEmits<SignatureEmits>()

const canvasRef = ref<HTMLCanvasElement>()
const isEmpty = ref(true)
const uploading = ref(false)
const isDrawing = ref(false)

let ctx: CanvasRenderingContext2D | null = null
let lastX = 0
let lastY = 0

// 初始化画布
onMounted(async () => {
	await nextTick()
	if (canvasRef.value) {
		const canvas = canvasRef.value
		const container = canvas.parentElement

		// 设置画布尺寸
		const dpr = window.devicePixelRatio || 1
		const rect = container?.getBoundingClientRect()

		if (rect) {
			canvas.width = rect.width * dpr
			canvas.height = rect.height * dpr
			canvas.style.width = rect.width + 'px'
			canvas.style.height = rect.height + 'px'
		}

		ctx = canvas.getContext('2d')
		if (ctx) {
			ctx.scale(dpr, dpr)
			ctx.lineCap = 'round'
			ctx.lineJoin = 'round'
			ctx.strokeStyle = '#000000'
			ctx.lineWidth = 3

			// 设置白色背景
			ctx.fillStyle = '#ffffff'
			ctx.fillRect(0, 0, canvas.width / dpr, canvas.height / dpr)
		}
	}
})

// 获取触摸或鼠标位置
const getEventPos = (e: TouchEvent | MouseEvent) => {
	const canvas = canvasRef.value
	if (!canvas) return { x: 0, y: 0 }

	const rect = canvas.getBoundingClientRect()
	let clientX: number, clientY: number

	if (e.type.includes('touch')) {
		const touch = (e as TouchEvent).touches[0] || (e as TouchEvent).changedTouches[0]
		clientX = touch.clientX
		clientY = touch.clientY
	} else {
		const mouse = e as MouseEvent
		clientX = mouse.clientX
		clientY = mouse.clientY
	}

	return {
		x: clientX - rect.left,
		y: clientY - rect.top
	}
}

// 开始绘制
const startDraw = (e: TouchEvent | MouseEvent) => {
	e.preventDefault()
	if (!ctx) return

	isDrawing.value = true
	const pos = getEventPos(e)
	lastX = pos.x
	lastY = pos.y

	ctx.beginPath()
	ctx.moveTo(lastX, lastY)
}

// 绘制过程
const drawing = (e: TouchEvent | MouseEvent) => {
	e.preventDefault()
	if (!isDrawing.value || !ctx) return

	const pos = getEventPos(e)

	ctx.lineTo(pos.x, pos.y)
	ctx.stroke()

	lastX = pos.x
	lastY = pos.y

	isEmpty.value = false
}

// 结束绘制
const endDraw = (e: TouchEvent | MouseEvent) => {
	e.preventDefault()
	if (!isDrawing.value) return

	isDrawing.value = false
	if (ctx) {
		ctx.closePath()
	}
}

// 清除签名
const clearSignature = () => {
	if (!ctx || !canvasRef.value) return

	const canvas = canvasRef.value
	ctx.fillStyle = '#ffffff'
	ctx.fillRect(0, 0, canvas.width / window.devicePixelRatio, canvas.height / window.devicePixelRatio)
	isEmpty.value = true
}

// 将画布转换为Blob
const canvasToBlob = (): Promise<Blob> => {
	return new Promise((resolve, reject) => {
		if (!canvasRef.value) {
			reject(new Error('Canvas not found'))
			return
		}

		canvasRef.value.toBlob((blob) => {
			if (blob) {
				resolve(blob)
			} else {
				reject(new Error('Failed to convert canvas to blob'))
			}
		}, 'image/png', 0.9)
	})
}

// 上传图片
const uploadSignature = async (file: Blob): Promise<UploadResponse> => {
  try {
    const response = await uploadImage(file)
    console.log('📤 签字图片上传响应:', response)
    
    // 根据实际API返回格式获取图片数据
    if (response.code === 200 && response.data) {
      return response.data as UploadResponse
    } else {
      throw new Error(response.msg || '上传失败')
    }
  } catch (error) {
    console.error('Upload error:', error)
    throw error
  }
}

// 确认签字
const confirmSignature = async () => {
	if (isEmpty.value) {
		showToast('请先签字')
		return
	}

	try {
		uploading.value = true

		// 转换为Blob
		const originalBlob = await canvasToBlob()

		// 压缩图片以减少上传大小
		const compressedFile = new File([originalBlob], 'signature.png', { type: 'image/png' })
		const compressedBlob = await compressImage(compressedFile, 0.8, 800, 600)

		// 上传图片
		const uploadResponse = await uploadSignature(compressedBlob)

		// 发送确认事件，传递完整的上传响应数据
		emit('confirm', uploadResponse)

		showToast({
			type: 'success',
			message: '签字上传成功'
		})
	} catch (error) {
		console.error('签字确认失败:', error)
		showToast({
			type: 'fail',
			message: '签字上传失败，请重试'
		})
	} finally {
		uploading.value = false
	}
}
</script>

<style scoped>
.signature-canvas {
	background: #ffffff;
	border-radius: 12px;
	overflow: hidden;
}

.signature-container {
	position: relative;
	width: 100%;
	height: 380px;
	border: 2px dashed #E5E7EB;
	border-radius: 8px;
	background: #ffffff;
}

.signature-board {
	display: block;
	width: 100%;
	height: 100%;
	cursor: crosshair;
	touch-action: none;
}

.signature-placeholder {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	color: #9CA3AF;
	font-size: 16px;
	pointer-events: none;
	user-select: none;
}

.signature-actions {
	display: flex;
	gap: 16px;
	padding: 20px;
	background: #F9FAFB;
}

.clear-btn {
	flex: 1;
	height: 60px !important;
	border-color: #E5E7EB;
	color: #6B7280;
}

.confirm-btn {
	flex: 2;
	height: 60px !important;
	background: #3583FF;
	border-color: #3583FF;
}

.confirm-btn:disabled {
	background: #E5E7EB;
	border-color: #E5E7EB;
	color: #9CA3AF;
}
</style>