<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showLoadingToast, closeToast, showConfirmDialog } from 'vant'
import { 
    getExitProcessDetail, 
    submitExitProcess,
    type ExitProcessDTO, 
    type ExitAssetDTO, 
    type ExitUtilityDTO,
    type ExitVo,
    type ExitRoomVo
} from '../api/exit'

// 定义扩展的数据类型
interface ExitDetailVo {
    exitInfo: ExitVo
    contractTerminateInfo: {
        terminateType: number
        terminateDate: string
    }
    exitRoomList: ExtendedExitRoomVo[]
    exitCostList: {
        subjectName: string
        amount: number
        startDate: string
        endDate: string
        receivableDate: string
        remark: string
    }[]
}

interface ExtendedExitRoomVo extends ExitRoomVo {
    selected?: boolean
    roomArea?: number
    electricReading?: string
    waterReading?: string
    gasReading?: string
    assetList?: ExitAssetDTO[]
}

// 路由
const router = useRouter()
const route = useRoute()

// 页面状态
const activeTab = ref('terminate')
const loading = ref(false)
const submitLoading = ref(false)

// 出场详情数据
const exitDetail = ref<ExitDetailVo | null>(null)
const exitRoomList = ref<ExtendedExitRoomVo[]>([])

// 物业交割相关
const selectAll = ref(false)
const searchKeyword = ref('')
const batchExitDate = ref('')
const batchExitDateModel = ref<string[]>([])
const showBatchDatePicker = ref(false)

// 房源日期选择
const showRoomDatePickerModal = ref(false)
const roomExitDateModel = ref<string[]>([])
const currentEditingRoom = ref<ExtendedExitRoomVo | null>(null)
const currentEditingIndex = ref(-1)

// 资产管理相关
const showAssetModal = ref(false)
const showAddAssetModal = ref(false)
const newAssetName = ref('')
const currentRoomAssets = ref<ExitAssetDTO[]>([])
const currentAssetRoom = ref<ExtendedExitRoomVo | null>(null)
const currentAssetIndex = ref(-1)

// 费用结算表单
const settlementForm = reactive({
    isDiscount: false,
    discountAmount: 0,
    discountReason: '',
    finalAmount: 0,
    refundProcessType: '1',
    payeeName: '',
    payeeAccount: '',
    bankName: '',
    licenseStatus: '1',
    taxCertStatus: '1',
    refundApplyType: '1',
    signType: '1',
    signAttachments: '',
})

// 减免设置弹框
const showDiscountDialog = ref(false)
const discountForm = reactive({
    isDiscount: false,
    discountAmount: 0,
    discountReason: ''
})

// 二维码相关
const qrcodeVisible = ref(false)

// 计算属性
const showPropertyTab = computed(() => {
    return props.mode === 'property-only' || props.mode === 'view'
})

const showSettlementTab = computed(() => {
    return props.mode === 'property-only' || props.mode === 'view'
})

const showCombinedTab = computed(() => {
    return props.mode === 'property-and-settlement'
})

const filteredRoomList = computed(() => {
    if (!searchKeyword.value) return exitRoomList.value
    return exitRoomList.value.filter(room => 
        room.buildingName?.includes(searchKeyword.value) || 
        room.roomName?.includes(searchKeyword.value)
    )
})

const hasSelectedRooms = computed(() => {
    return exitRoomList.value.some(room => room.selected)
})

const allRoomsConfirmed = computed(() => {
    if (!exitRoomList.value.length) return false
    
    if (props.mode === 'property-and-settlement') {
        return exitRoomList.value.every(room =>
            room.isBusinessConfirmed &&
            room.isEngineeringConfirmed
        )
    }

    return exitRoomList.value.every(room =>
        room.isBusinessConfirmed &&
        room.isEngineeringConfirmed &&
        room.isFinanceConfirmed
    )
})

const totalAmount = computed(() => {
    return (exitDetail.value?.exitCostList || []).reduce((sum, item) => sum + (item.amount || 0), 0)
})

const finalAmount = computed(() => {
    return totalAmount.value - (settlementForm.discountAmount || 0)
})

// Props定义
const props = defineProps({
    mode: {
        type: String,
        default: 'property-and-settlement' // 'property-only' 或 'property-and-settlement' 或 'view'
    }
})

// 页面方法
const handleBack = () => {
    router.back()
}

const formatDate = (date: string | Date | null) => {
    if (!date) return ''
    const d = typeof date === 'string' ? new Date(date) : date
    return d.toISOString().split('T')[0]
}

// 获取退租类型文本
const getTerminateTypeText = (type: number) => {
    const typeMap: Record<number, string> = {
        1: '到期退租',
        2: '提前退租',
        3: '违约退租'
    }
    return typeMap[type] || '未知'
}

// 获取物业类型文本
const getPropertyTypeText = (type: number) => {
    const typeMap: Record<number, string> = {
        1: '住宅',
        2: '厂房',
        3: '商铺'
    }
    return typeMap[type] || '未知'
}

// 获取确认状态类型
const getConfirmStatusType = (room: any) => {
    if (room.isBusinessConfirmed && room.isEngineeringConfirmed && room.isFinanceConfirmed) {
        return 'success'
    } else if (room.isBusinessConfirmed || room.isEngineeringConfirmed) {
        return 'warning'
    } else {
        return 'default'
    }
}

// 获取确认状态文本
const getConfirmStatusText = (room: ExtendedExitRoomVo) => {
    const confirmed = [
        room.isBusinessConfirmed && '商务',
        room.isEngineeringConfirmed && '工程',
        room.isFinanceConfirmed && '财务'
    ].filter(Boolean)
    
    if (confirmed.length === 0) return '待确认'
    if (confirmed.length === 3) return '已确认'
    return `部分确认(${confirmed.join('、')})`
}

// 批量操作
const handleSelectAll = () => {
    selectAll.value = !selectAll.value
    exitRoomList.value.forEach(room => {
        room.selected = selectAll.value
    })
}

const handleSelectSingle = (room: any, index: number) => {
    selectAll.value = exitRoomList.value.every(r => r.selected)
}

const onBatchDateConfirm = (value: any) => {
    if (Array.isArray(value) && value.length >= 3) {
        batchExitDate.value = `${value[0]}-${value[1].padStart(2, '0')}-${value[2].padStart(2, '0')}`
    }
    showBatchDatePicker.value = false
}

const handleBatchSetExitDate = () => {
    if (!batchExitDate.value) {
        showToast('请先选择出场日期')
        return
    }
    
    exitRoomList.value.forEach(room => {
        if (room.selected) {
            room.exitDate = batchExitDate.value
        }
    })
    showToast('批量设置成功')
}

const handleBatchConfirm = async () => {
    try {
        showLoadingToast('批量确认中...')
        
        const selectedRooms = exitRoomList.value.filter(room => room.selected)
        // 这里调用批量确认API
        
        closeToast()
        showToast('批量确认成功')
    } catch (error) {
        closeToast()
        showToast('批量确认失败')
    }
}

const handleCopyPropertyUrl = () => {
    qrcodeVisible.value = true
}

// 房源日期选择
const showRoomDatePicker = (room: any, index: number) => {
    currentEditingRoom.value = room
    currentEditingIndex.value = index
    if (room.exitDate) {
        const date = new Date(room.exitDate)
        roomExitDateModel.value = [
            date.getFullYear().toString(),
            (date.getMonth() + 1).toString(),
            date.getDate().toString()
        ]
    }
    showRoomDatePickerModal.value = true
}

const onRoomDateConfirm = (value: any) => {
    if (Array.isArray(value) && value.length >= 3 && currentEditingRoom.value) {
        const dateStr = `${value[0]}-${value[1].padStart(2, '0')}-${value[2].padStart(2, '0')}`
        currentEditingRoom.value.exitDate = dateStr
    }
    showRoomDatePickerModal.value = false
}

// 资产管理
const showAssetDialog = (room: any, index: number) => {
    currentAssetRoom.value = room
    currentAssetIndex.value = index
    currentRoomAssets.value = room.assetList || []
    showAssetModal.value = true
}

const addAsset = () => {
    showAddAssetModal.value = true
    newAssetName.value = ''
}

const confirmAddAsset = () => {
    if (!newAssetName.value.trim()) {
        showToast('请输入资产名称')
        return
    }
    
    currentRoomAssets.value.push({
        assetName: newAssetName.value.trim(),
        isNormal: 1
    })
    
    showAddAssetModal.value = false
    newAssetName.value = ''
}

const removeAsset = (index: number) => {
    currentRoomAssets.value.splice(index, 1)
}

const saveAssets = () => {
    if (currentAssetRoom.value) {
        currentAssetRoom.value.assetList = [...currentRoomAssets.value]
    }
    showAssetModal.value = false
    showToast('资产保存成功')
}

// 房源操作
const handleSaveRoom = async (room: ExtendedExitRoomVo, index: number) => {
    try {
        showLoadingToast('保存中...')
        
        // 这里应该调用保存房源的API
        console.log('保存房源数据:', room)
        
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        closeToast()
        showToast('保存成功')
    } catch (error) {
        closeToast()
        showToast('保存失败')
    }
}

const handleCancelRoom = (room: any, index: number) => {
    // 重置房源数据
    initPageData()
}

// 减免设置
const handleDiscountConfirm = () => {
    settlementForm.isDiscount = discountForm.isDiscount
    settlementForm.discountAmount = discountForm.discountAmount
    settlementForm.discountReason = discountForm.discountReason
    showDiscountDialog.value = false
    showToast('减免设置成功')
}

const handleDiscountCancel = () => {
    showDiscountDialog.value = false
    // 重置减免表单
    discountForm.isDiscount = settlementForm.isDiscount
    discountForm.discountAmount = settlementForm.discountAmount
    discountForm.discountReason = settlementForm.discountReason
}

// 上传附件
const handleUploadAttachment = () => {
    showToast('上传功能开发中')
}

// 底部按钮操作
const handleCancel = () => {
    router.back()
}

const handleNextStep = () => {
    activeTab.value = 'settlement'
}

const handleImmediateSettlement = () => {
    activeTab.value = 'settlement'
}

const handleSaveOnly = async () => {
    try {
        showLoadingToast('保存中...')
        
        console.log('保存结算数据:', settlementForm)
        
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        closeToast()
        showToast('保存成功')
    } catch (error) {
        closeToast()
        showToast('保存失败')
    }
}

const handleSubmitSettlement = async () => {
    const confirmResult = await showConfirmDialog({
        title: '确认提交',
        message: '确认提交结算单吗？提交后将无法修改。',
    })

    if (confirmResult !== 'confirm') {
        return
    }

    try {
        showLoadingToast('提交中...')
        
        console.log('提交结算数据:', settlementForm)
        
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        closeToast()
        showToast('提交成功')
        
        setTimeout(() => {
            router.back()
        }, 1500)
    } catch (error) {
        closeToast()
        showToast('提交失败')
    }
}

const handleSaveOnlyAndSettlement = () => {
    handleSaveOnly()
}

const handleSubmitSettlementAndProperty = () => {
    handleSubmitSettlement()
}

// 初始化数据
const initPageData = async () => {
    const exitId = route.params.id || route.query.id
    
    try {
        showLoadingToast({
            message: '加载中...',
            duration: 0
        })

        const res = await getExitProcessDetail(exitId as string)
        
        closeToast()
        
        if (res.code === 200 && res.data) {
            exitDetail.value = res.data
            
            // 初始化房源列表
            exitRoomList.value = (res.data.exitRoomList || []).map(room => ({
                ...room,
                selected: false,
                assetList: room.assetList || []
            }))
            
            // 初始化结算表单
            if (res.data.exitInfo) {
                Object.assign(settlementForm, {
                    isDiscount: res.data.exitInfo.isDiscount || false,
                    discountAmount: res.data.exitInfo.discountAmount || 0,
                    discountReason: res.data.exitInfo.discountReason || '',
                    refundProcessType: res.data.exitInfo.refundProcessType?.toString() || '1',
                    payeeName: res.data.exitInfo.payeeName || '',
                    payeeAccount: res.data.exitInfo.payeeAccount || '',
                    bankName: res.data.exitInfo.bankName || '',
                    licenseStatus: res.data.exitInfo.licenseStatus?.toString() || '1',
                    taxCertStatus: res.data.exitInfo.taxCertStatus?.toString() || '1',
                    refundApplyType: res.data.exitInfo.refundApplyType?.toString() || '1',
                    signType: res.data.exitInfo.signType?.toString() || '1',
                    signAttachments: res.data.exitInfo.signAttachments || ''
                })
            }
        } else {
            // 使用模拟数据
            exitDetail.value = {
                exitInfo: {
                    contractNo: 'WYZF-2024-0623',
                    customerName: '苏州市科技发展有限公司',
                    contractPurpose: 1,
                    processType: props.mode === 'property-and-settlement' ? 2 : 1
                },
                contractTerminateInfo: {
                    terminateType: 1,
                    terminateDate: '2025-04-19'
                },
                exitRoomList: [
                    {
                        id: '1',
                        buildingName: '北辰·兰郡5号楼',
                        roomName: '101',
                        roomArea: 100,
                        propertyType: 2,
                        exitDate: '',
                        isBusinessConfirmed: false,
                        isEngineeringConfirmed: false,
                        isFinanceConfirmed: false,
                        electricReading: '',
                        waterReading: '',
                        gasReading: '',
                        assetList: []
                    }
                ],
                exitCostList: [
                    {
                        subjectName: '租金',
                        amount: 10000,
                        startDate: '2024-01-01',
                        endDate: '2024-12-31',
                        receivableDate: '2025-01-01',
                        remark: ''
                    }
                ]
            }
            
            exitRoomList.value = exitDetail.value.exitRoomList.map(room => ({
                ...room,
                selected: false
            }))
        }
        
        // 根据模式设置初始Tab
        if (props.mode === 'property-only') {
            activeTab.value = 'property'
        } else if (props.mode === 'property-and-settlement') {
            activeTab.value = 'combined'
        }
        
    } catch (error) {
        closeToast()
        console.error('获取出场详情失败:', error)
        showToast('获取出场详情失败')
    }
}

// 监听模式变化
watch(() => props.mode, (newMode) => {
    if (newMode === 'property-only') {
        activeTab.value = 'property'
    } else if (newMode === 'property-and-settlement') {
        activeTab.value = 'combined'
    }
}, { immediate: true })

onMounted(() => {
    initPageData()
})
</script>

<template>
    <div class="exit-process">
        <!-- 顶部导航 -->
        <van-nav-bar title="出场办理" left-text="返回" left-arrow @click-left="handleBack" />

        <!-- Tab切换 -->
        <van-tabs v-model:active="activeTab" sticky>
            <!-- 退租申请 -->
            <van-tab title="退租申请" name="terminate">
                <div class="tab-content">
                    <!-- 基本信息 -->
                    <div class="section-card">
                        <div class="section-header">
                            <span class="section-title">基本信息</span>
                        </div>
                        <div class="info-content">
                            <div class="info-row">
                                <span class="label">合同编号:</span>
                                <span class="value">{{ exitDetail?.exitInfo?.contractNo || '-' }}</span>
                            </div>
                            <div class="info-row">
                                <span class="label">承租方:</span>
                                <span class="value">{{ exitDetail?.exitInfo?.customerName || '-' }}</span>
                            </div>
                            <div class="info-row">
                                <span class="label">退租类型:</span>
                                <span class="value">{{ getTerminateTypeText(exitDetail?.contractTerminateInfo?.terminateType) }}</span>
                            </div>
                            <div class="info-row">
                                <span class="label">退租日期:</span>
                                <span class="value">{{ exitDetail?.contractTerminateInfo?.terminateDate || '-' }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 退租房源 -->
                    <div class="section-card">
                        <div class="section-header">
                            <span class="section-title">退租房源</span>
                        </div>
                        <div class="room-list">
                            <div 
                                v-for="(room, index) in exitDetail?.exitRoomList || []" 
                                :key="index"
                                class="room-item"
                            >
                                <div class="room-info">
                                    <div class="room-name">{{ room.buildingName }} {{ room.roomName }}</div>
                                    <div class="room-meta">
                                        <span>面积: {{ room.roomArea }}㎡</span>
                                        <span>类型: {{ getPropertyTypeText(room.propertyType) }}</span>
                                    </div>
                                </div>
                                <div class="room-status">
                                    <van-tag 
                                        :type="room.isBusinessConfirmed ? 'primary' : 'default'"
                                        size="small"
                                    >
                                        {{ room.isBusinessConfirmed ? '已确认' : '待确认' }}
                                    </van-tag>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </van-tab>

            <!-- 物业交割 -->
            <van-tab title="物业交割" name="property" v-if="showPropertyTab">
                <div class="tab-content">
                    <!-- 批量操作 -->
                    <div class="section-card">
                        <div class="section-header">
                            <span class="section-title">批量操作</span>
                            <van-button 
                                type="primary" 
                                size="mini" 
                                @click="handleSelectAll"
                            >
                                {{ selectAll ? '取消全选' : '全选' }}
                            </van-button>
                        </div>
                        <div class="batch-actions">
                            <div class="batch-row">
                                <span class="batch-label">批量设置出场日期:</span>
                                <van-field
                                    v-model="batchExitDate"
                                    placeholder="选择出场日期"
                                    readonly
                                    is-link
                                    @click="showBatchDatePicker = true"
                                />
                                <van-button 
                                    type="primary" 
                                    size="mini" 
                                    @click="handleBatchSetExitDate"
                                    :disabled="!hasSelectedRooms"
                                >
                                    应用
                                </van-button>
                            </div>
                            <div class="batch-row">
                                <van-button 
                                    type="primary" 
                                    @click="handleBatchConfirm"
                                    :disabled="!hasSelectedRooms"
                                >
                                    批量确认
                                </van-button>
                                <van-button 
                                    @click="handleCopyPropertyUrl"
                                    :disabled="!hasSelectedRooms"
                                >
                                    生成确认链接
                                </van-button>
                            </div>
                        </div>
                    </div>

                    <!-- 房源交割列表 -->
                    <div class="section-card">
                        <div class="section-header">
                            <span class="section-title">房源交割</span>
                            <van-field
                                v-model="searchKeyword"
                                placeholder="搜索房源"
                                size="small"
                                style="width: 200px;"
                            />
                        </div>
                        
                        <div class="property-list">
                            <div 
                                v-for="(room, index) in filteredRoomList" 
                                :key="index"
                                class="property-item"
                                :class="{ 'selected': room.selected }"
                            >
                                <div class="property-header">
                                    <van-checkbox 
                                        v-model="room.selected" 
                                        @change="handleSelectSingle(room, index)"
                                    />
                                    <div class="property-info">
                                        <div class="property-name">{{ room.buildingName }} {{ room.roomName }}</div>
                                        <div class="property-meta">
                                            面积: {{ room.roomArea }}㎡ | 
                                            类型: {{ getPropertyTypeText(room.propertyType) }}
                                        </div>
                                    </div>
                                    <div class="property-status">
                                        <van-tag 
                                            :type="getConfirmStatusType(room)"
                                            size="small"
                                        >
                                            {{ getConfirmStatusText(room) }}
                                        </van-tag>
                                    </div>
                                </div>

                                <div class="property-details">
                                    <!-- 出场日期 -->
                                    <div class="detail-row">
                                        <span class="detail-label">出场日期:</span>
                                        <van-field
                                            v-model="room.exitDate"
                                            placeholder="选择出场日期"
                                            readonly
                                            is-link
                                            @click="showRoomDatePicker(room, index)"
                                        />
                                    </div>

                                    <!-- 房间配套情况 -->
                                    <div class="detail-row">
                                        <span class="detail-label">房间配套:</span>
                                        <van-button 
                                            size="mini" 
                                            @click="showAssetDialog(room, index)"
                                        >
                                            管理配套
                                        </van-button>
                                    </div>

                                    <!-- 水电度数 -->
                                    <div class="utility-section">
                                        <div class="utility-title">水电度数</div>
                                        <div class="utility-grid">
                                            <div class="utility-item">
                                                <span class="utility-label">电表:</span>
                                                <van-field
                                                    v-model="room.electricReading"
                                                    type="number"
                                                    placeholder="度数"
                                                />
                                            </div>
                                            <div class="utility-item">
                                                <span class="utility-label">水表:</span>
                                                <van-field
                                                    v-model="room.waterReading"
                                                    type="number"
                                                    placeholder="度数"
                                                />
                                            </div>
                                            <div class="utility-item">
                                                <span class="utility-label">燃气:</span>
                                                <van-field
                                                    v-model="room.gasReading"
                                                    type="number"
                                                    placeholder="度数"
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 操作按钮 -->
                                    <div class="property-actions">
                                        <van-button 
                                            type="primary" 
                                            size="small"
                                            @click="handleSaveRoom(room, index)"
                                        >
                                            保存
                                        </van-button>
                                        <van-button 
                                            size="small"
                                            @click="handleCancelRoom(room, index)"
                                        >
                                            取消
                                        </van-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </van-tab>

            <!-- 费用结算 -->
            <van-tab title="费用结算" name="settlement" v-if="showSettlementTab" :disabled="!allRoomsConfirmed">
                <div class="tab-content">
                    <!-- 费用明细 -->
                    <div class="section-card">
                        <div class="section-header">
                            <span class="section-title">费用明细</span>
                            <span class="total-amount">总计: ¥{{ totalAmount.toFixed(2) }}</span>
                        </div>
                        
                        <div class="cost-list">
                            <div 
                                v-for="(cost, index) in exitDetail?.exitCostList || []" 
                                :key="index"
                                class="cost-item"
                            >
                                <div class="cost-header">
                                    <span class="cost-subject">{{ cost.subjectName }}</span>
                                    <span class="cost-amount">¥{{ cost.amount?.toFixed(2) || '0.00' }}</span>
                                </div>
                                <div class="cost-details">
                                    <div class="cost-row">
                                        <span class="cost-label">计费周期:</span>
                                        <span class="cost-value">{{ cost.startDate }} 至 {{ cost.endDate }}</span>
                                    </div>
                                    <div class="cost-row">
                                        <span class="cost-label">应收日期:</span>
                                        <span class="cost-value">{{ cost.receivableDate }}</span>
                                    </div>
                                    <div class="cost-row" v-if="cost.remark">
                                        <span class="cost-label">备注:</span>
                                        <span class="cost-value">{{ cost.remark }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <van-empty v-if="!exitDetail?.exitCostList?.length" description="暂无费用明细" />
                    </div>

                    <!-- 减免设置 -->
                    <div class="section-card">
                        <div class="section-header">
                            <span class="section-title">减免设置</span>
                            <van-button 
                                type="primary" 
                                size="mini"
                                @click="showDiscountDialog = true"
                            >
                                设置减免
                            </van-button>
                        </div>
                        <div class="discount-info">
                            <div class="info-row">
                                <span class="label">是否减免:</span>
                                <span class="value">{{ settlementForm.isDiscount ? '是' : '否' }}</span>
                            </div>
                            <div class="info-row" v-if="settlementForm.isDiscount">
                                <span class="label">减免金额:</span>
                                <span class="value discount-amount">¥{{ settlementForm.discountAmount?.toFixed(2) || '0.00' }}</span>
                            </div>
                            <div class="info-row" v-if="settlementForm.isDiscount && settlementForm.discountReason">
                                <span class="label">减免原因:</span>
                                <span class="value">{{ settlementForm.discountReason }}</span>
                            </div>
                            <div class="info-row">
                                <span class="label">最终费用:</span>
                                <span class="value final-amount">¥{{ finalAmount.toFixed(2) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 退款信息 -->
                    <div class="section-card">
                        <div class="section-header">
                            <span class="section-title">退款信息</span>
                        </div>
                        <div class="refund-form">
                            <van-field
                                v-model="settlementForm.payeeName"
                                label="收款人"
                                placeholder="请输入收款人姓名"
                                clearable
                            />
                            <van-field
                                v-model="settlementForm.payeeAccount"
                                label="收款账号"
                                placeholder="请输入收款账号"
                                clearable
                            />
                            <van-field
                                v-model="settlementForm.bankName"
                                label="开户银行"
                                placeholder="请输入开户银行"
                                clearable
                            />
                        </div>

                        <div class="form-section">
                            <div class="section-subtitle">退款处理方式</div>
                            <van-radio-group v-model="settlementForm.refundProcessType">
                                <van-radio name="1">退款</van-radio>
                                <van-radio name="2">暂存客户账户</van-radio>
                            </van-radio-group>
                        </div>

                        <div class="form-section">
                            <div class="section-subtitle">退款申请方式</div>
                            <van-radio-group v-model="settlementForm.refundApplyType">
                                <van-radio name="1">只结算，暂不退款</van-radio>
                                <van-radio name="2">结算并申请退款</van-radio>
                            </van-radio-group>
                        </div>
                    </div>

                    <!-- 证照办理情况 -->
                    <div class="section-card">
                        <div class="section-header">
                            <span class="section-title">证照办理情况</span>
                        </div>
                        
                        <div class="form-section">
                            <div class="section-subtitle">营业执照办理情况</div>
                            <van-radio-group v-model="settlementForm.licenseStatus">
                                <van-radio name="1">未办理执照</van-radio>
                                <van-radio name="2">需注销</van-radio>
                                <van-radio name="3">已注销</van-radio>
                            </van-radio-group>
                        </div>

                        <div class="form-section">
                            <div class="section-subtitle">税务登记证办理情况</div>
                            <van-radio-group v-model="settlementForm.taxCertStatus">
                                <van-radio name="1">未办理执照</van-radio>
                                <van-radio name="2">需注销</van-radio>
                                <van-radio name="3">已注销</van-radio>
                            </van-radio-group>
                        </div>
                    </div>

                    <!-- 签字方式 -->
                    <div class="section-card">
                        <div class="section-header">
                            <span class="section-title">签字方式</span>
                        </div>
                        
                        <van-radio-group v-model="settlementForm.signType">
                            <van-radio name="1">线上</van-radio>
                            <van-radio name="2">线下</van-radio>
                        </van-radio-group>

                        <van-field
                            v-if="settlementForm.signType === '2'"
                            v-model="settlementForm.signAttachments"
                            label="签字附件"
                            placeholder="请上传签字附件"
                            readonly
                            is-link
                            @click="handleUploadAttachment"
                        />
                    </div>
                </div>
            </van-tab>

            <!-- 物业交割和费用结算 -->
            <van-tab title="物业交割和费用结算" name="combined" v-if="showCombinedTab">
                <div class="tab-content">
                    <!-- 复用物业交割内容 -->
                    <div class="combined-section">
                        <div class="combined-title">物业交割</div>
                        <!-- 这里可以复用物业交割的内容 -->
                    </div>

                    <!-- 复用费用结算内容 -->
                    <div class="combined-section">
                        <div class="combined-title">费用结算</div>
                        <!-- 这里可以复用费用结算的内容 -->
                    </div>
                </div>
            </van-tab>
        </van-tabs>

        <!-- 底部按钮 -->
        <div class="footer-actions">
            <van-button @click="handleCancel">取消</van-button>
            
            <!-- 物业交割阶段按钮 -->
            <template v-if="activeTab === 'property'">
                <van-button 
                    type="primary" 
                    :disabled="!allRoomsConfirmed" 
                    @click="handleNextStep"
                >
                    进入下一步费用结算
                </van-button>
                <van-button 
                    type="warning" 
                    @click="handleImmediateSettlement"
                >
                    立即结算
                </van-button>
            </template>

            <!-- 费用结算阶段按钮 -->
            <template v-if="activeTab === 'settlement'">
                <van-button type="success" @click="handleSaveOnly">暂存</van-button>
                <van-button type="primary" @click="handleSubmitSettlement">提交结算单</van-button>
            </template>

            <!-- 物业交割和费用结算组合阶段按钮 -->
            <template v-if="activeTab === 'combined'">
                <van-button type="success" @click="handleSaveOnlyAndSettlement">暂存</van-button>
                <van-button type="primary" @click="handleSubmitSettlementAndProperty">提交结算单</van-button>
            </template>
        </div>

        <!-- 日期选择器弹框 -->
        <van-popup v-model:show="showBatchDatePicker" position="bottom">
            <van-date-picker
                v-model="batchExitDateModel"
                title="选择批量出场日期"
                @confirm="onBatchDateConfirm"
                @cancel="showBatchDatePicker = false"
            />
        </van-popup>

        <van-popup v-model:show="showRoomDatePickerModal" position="bottom">
            <van-date-picker
                v-model="roomExitDateModel"
                title="选择出场日期"
                @confirm="onRoomDateConfirm"
                @cancel="showRoomDatePickerModal = false"
            />
        </van-popup>

        <!-- 资产管理弹框 -->
        <van-popup v-model:show="showAssetModal" position="bottom" style="height: 60%;">
            <div class="asset-management">
                <div class="asset-header">
                    <span class="asset-title">房间配套管理</span>
                    <van-button 
                        type="primary" 
                        size="mini"
                        @click="addAsset"
                    >
                        + 添加
                    </van-button>
                </div>
                <div class="asset-list">
                    <div 
                        v-for="(asset, index) in currentRoomAssets" 
                        :key="index"
                        class="asset-item"
                    >
                        <div class="asset-info">
                            <span class="asset-name">{{ asset.assetName }}</span>
                            <van-switch 
                                v-model="asset.isNormal" 
                                :active-value="1" 
                                :inactive-value="0"
                                active-text="正常"
                                inactive-text="损坏"
                            />
                        </div>
                        <van-icon 
                            name="delete-o" 
                            @click="removeAsset(index)"
                        />
                    </div>
                </div>
                <div class="asset-actions">
                    <van-button @click="showAssetModal = false">取消</van-button>
                    <van-button type="primary" @click="saveAssets">保存</van-button>
                </div>
            </div>
        </van-popup>

        <!-- 添加资产弹框 -->
        <van-popup v-model:show="showAddAssetModal" position="bottom">
            <div class="add-asset-dialog">
                <div class="dialog-header">
                    <span class="dialog-title">添加资产</span>
                </div>
                <div class="dialog-content">
                    <van-field
                        v-model="newAssetName"
                        label="资产名称"
                        placeholder="请输入资产名称"
                        clearable
                    />
                </div>
                <div class="dialog-actions">
                    <van-button @click="showAddAssetModal = false">取消</van-button>
                    <van-button type="primary" @click="confirmAddAsset">确定</van-button>
                </div>
            </div>
        </van-popup>

        <!-- 减免设置弹框 -->
        <van-popup v-model:show="showDiscountDialog" position="bottom">
            <div class="discount-dialog">
                <div class="dialog-header">
                    <span class="dialog-title">减免设置</span>
                </div>
                <div class="dialog-content">
                    <van-field label="是否减免">
                        <template #input>
                            <van-switch v-model="discountForm.isDiscount" />
                        </template>
                    </van-field>
                    
                    <van-field
                        v-if="discountForm.isDiscount"
                        v-model="discountForm.discountAmount"
                        type="number"
                        label="减免金额"
                        placeholder="请输入减免金额"
                        clearable
                    />
                    
                    <van-field
                        v-if="discountForm.isDiscount"
                        v-model="discountForm.discountReason"
                        type="textarea"
                        label="减免原因"
                        placeholder="请输入减免原因"
                        rows="3"
                        clearable
                    />
                </div>
                <div class="dialog-actions">
                    <van-button @click="handleDiscountCancel">取消</van-button>
                    <van-button type="primary" @click="handleDiscountConfirm">确定</van-button>
                </div>
            </div>
        </van-popup>

        <!-- 物业确认二维码弹框 -->
        <van-popup v-model:show="qrcodeVisible" position="center" :style="{ width: '80%', maxWidth: '400px' }">
            <div class="qrcode-container">
                <div class="qrcode-header">
                    <div class="qrcode-title">物业确认单</div>
                    <div class="exit-info">
                        <div class="info-item">
                            <span class="label">合同号：</span>
                            <span class="value">{{ exitDetail?.exitInfo?.contractNo }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">承租方：</span>
                            <span class="value">{{ exitDetail?.exitInfo?.customerName }}</span>
                        </div>
                    </div>
                </div>
                <div class="qrcode-display">
                    <!-- 这里可以放置二维码组件 -->
                    <div class="qrcode-placeholder">
                        二维码生成区域
                    </div>
                </div>
                <div class="qrcode-actions">
                    <van-button @click="qrcodeVisible = false">关闭</van-button>
                </div>
            </div>
        </van-popup>
    </div>
</template>

<style scoped>
.exit-process {
    min-height: 100vh;
    background-color: #f5f5f5;
    padding-bottom: 200px;
}

/* Tab内容 */
.tab-content {
    padding: 32px;
}

/* 区块卡片 */
.section-card {
    margin-bottom: 24px;
    background: white;
    border-radius: 24px;
    overflow: hidden;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32px;
    background: #f8f9fa;
    border-bottom: 2px solid #eee;
}

.section-title {
    font-size: 32px;
    font-weight: 500;
    color: #333;
}

.total-amount {
    font-size: 28px;
    color: #ff4444;
    font-weight: 600;
}

/* 信息展示 */
.info-content {
    padding: 32px;
}

.info-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 28px;
}

.info-row:last-child {
    margin-bottom: 0;
}

.label {
    color: #666;
    flex-shrink: 0;
    width: 160px;
}

.value {
    color: #333;
    flex: 1;
}

.discount-amount {
    color: #ff4444;
    font-weight: 600;
}

.final-amount {
    color: #52c41a;
    font-weight: 600;
    font-size: 30px;
}

/* 房源列表 */
.room-list {
    padding: 0 32px;
}

.room-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32px 0;
    border-bottom: 2px solid #f0f0f0;
}

.room-item:last-child {
    border-bottom: none;
}

.room-info {
    flex: 1;
}

.room-name {
    font-size: 32px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.room-meta {
    font-size: 24px;
    color: #999;
}

.room-meta span {
    margin-right: 32px;
}

.room-status {
    margin-left: 16px;
}

/* 批量操作 */
.batch-actions {
    padding: 32px;
}

.batch-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 16px;
}

.batch-row:last-child {
    margin-bottom: 0;
}

.batch-label {
    font-size: 28px;
    color: #333;
    flex-shrink: 0;
    width: 200px;
}

/* 物业交割列表 */
.property-list {
    padding: 0 32px;
}

.property-item {
    border: 2px solid #f0f0f0;
    border-radius: 16px;
    margin-bottom: 24px;
    overflow: hidden;
}

.property-item.selected {
    border-color: #4A90E2;
}

.property-header {
    display: flex;
    align-items: center;
    padding: 32px;
    background: #f8f9fa;
    border-bottom: 2px solid #f0f0f0;
}

.property-info {
    flex: 1;
    margin-left: 16px;
}

.property-name {
    font-size: 32px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.property-meta {
    font-size: 24px;
    color: #999;
}

.property-status {
    margin-left: 16px;
}

.property-details {
    padding: 32px;
}

.detail-row {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    gap: 16px;
}

.detail-row:last-child {
    margin-bottom: 0;
}

.detail-label {
    font-size: 28px;
    color: #333;
    flex-shrink: 0;
    width: 120px;
}

/* 水电度数 */
.utility-section {
    margin: 24px 0;
}

.utility-title {
    font-size: 28px;
    color: #333;
    margin-bottom: 16px;
}

.utility-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.utility-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.utility-label {
    font-size: 24px;
    color: #333;
    flex-shrink: 0;
    width: 80px;
}

/* 操作按钮 */
.property-actions {
    display: flex;
    gap: 16px;
    margin-top: 24px;
}

/* 费用列表 */
.cost-list {
    padding: 0 32px;
}

.cost-item {
    border: 2px solid #f0f0f0;
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 24px;
}

.cost-item:last-child {
    margin-bottom: 0;
}

.cost-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f5f5f5;
}

.cost-subject {
    font-size: 32px;
    font-weight: 600;
    color: #333;
}

.cost-amount {
    font-size: 32px;
    color: #ff4444;
    font-weight: 600;
}

.cost-details {
    font-size: 26px;
}

.cost-row {
    display: flex;
    margin-bottom: 8px;
}

.cost-row:last-child {
    margin-bottom: 0;
}

.cost-label {
    color: #666;
    flex-shrink: 0;
    width: 120px;
}

.cost-value {
    color: #333;
    flex: 1;
}

/* 减免信息 */
.discount-info {
    padding: 32px;
}

/* 退款表单 */
.refund-form {
    padding: 32px;
}

.form-section {
    padding: 32px;
    border-top: 2px solid #f0f0f0;
}

.section-subtitle {
    font-size: 28px;
    color: #333;
    font-weight: 500;
    margin-bottom: 16px;
}

/* 组合Tab */
.combined-section {
    margin-bottom: 48px;
}

.combined-title {
    font-size: 36px;
    font-weight: 600;
    color: #333;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 4px solid #4A90E2;
}

/* 底部按钮 */
.footer-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 24px 32px;
    background: white;
    border-top: 2px solid #eee;
    display: flex;
    gap: 16px;
    z-index: 100;
}

.footer-actions .van-button {
    flex: 1;
    height: 88px;
    font-size: 28px;
}

/* 弹框样式 */
.asset-management {
    padding: 32px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.asset-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f0f0f0;
}

.asset-title {
    font-size: 32px;
    font-weight: 500;
    color: #333;
}

.asset-list {
    flex: 1;
    overflow-y: auto;
}

.asset-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 0;
    border-bottom: 2px solid #f0f0f0;
}

.asset-item:last-child {
    border-bottom: none;
}

.asset-info {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.asset-name {
    font-size: 28px;
    color: #333;
}

.asset-actions {
    display: flex;
    gap: 16px;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 2px solid #f0f0f0;
}

.asset-actions .van-button {
    flex: 1;
    height: 88px;
    font-size: 28px;
}

/* 添加资产弹框 */
.add-asset-dialog,
.discount-dialog {
    background: white;
    border-radius: 32px 32px 0 0;
}

.dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32px 40px;
    border-bottom: 2px solid #eee;
}

.dialog-title {
    font-size: 32px;
    font-weight: 500;
}

.dialog-content {
    padding: 40px;
}

.dialog-actions {
    display: flex;
    gap: 24px;
    margin: 40px;
    margin-top: 0;
}

.dialog-actions .van-button {
    flex: 1;
    height: 88px;
    font-size: 28px;
}

/* 二维码弹框 */
.qrcode-container {
    padding: 40px;
    text-align: center;
}

.qrcode-header {
    margin-bottom: 32px;
}

.qrcode-title {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin-bottom: 24px;
}

.exit-info {
    text-align: left;
}

.exit-info .info-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 24px;
}

.exit-info .label {
    color: #666;
    width: 120px;
    flex-shrink: 0;
}

.exit-info .value {
    color: #333;
}

.qrcode-display {
    margin: 32px 0;
}

.qrcode-placeholder {
    width: 400px;
    height: 400px;
    border: 4px dashed #ddd;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    color: #999;
    margin: 0 auto;
}

.qrcode-actions {
    margin-top: 32px;
}

.qrcode-actions .van-button {
    height: 88px;
    font-size: 28px;
    padding: 0 64px;
}
</style>
