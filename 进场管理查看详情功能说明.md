# 进场管理查看详情功能说明

## 功能概述
为进场管理模块新增了查看详情功能，用户可以查看已办理或待办理进场单的详细信息，包括合同基本信息、房源详情、配套设施、水电度数等完整信息。

## 功能特点

### 1. 详情入口
- 在进场管理列表页面（EntryManagement.vue）的每个进场项目中新增"查看详情"按钮
- 按钮样式为灰色，区别于其他操作按钮（调整-蓝色，通知客户-橙色）
- 支持待办理和已办理状态的进场单查看

### 2. 详情页面功能
- **基本信息展示**：合同编号、合同用途、租期、承租方、创建时间、创建人、状态
- **房源列表**：展示所有相关房源，支持展开/收起查看详情
- **房源详情**：楼栋、地块、进场日期等基本信息
- **配套设施**：显示房间配套情况，包括资产名称、规格、数量、状态（完好/缺失）
- **水电度数**：记录电表、冷水表、热水表读数（如有）
- **备注信息**：显示相关备注内容

### 3. 页面交互
- 移动端友好的卡片式布局
- 房源支持展开/收起操作
- 默认展开第一个房源
- 资产状态用不同颜色标识（完好-绿色，缺失-红色）

## 技术实现

### 1. 新增文件
- `src/views/EntryDetail.vue` - 进场详情页面组件（专用详情展示页面）

### 2. 修改文件
- `src/views/EntryManagement.vue` - 添加查看详情按钮和跳转方法
- `src/views/EntryProcess.vue` - 添加view模式支持，可用于只读查看
- `src/router/index.ts` - 添加进场详情页面路由配置

### 3. 两种查看方式
- **专用详情页面（EntryDetail.vue）**：设计精美，专门用于详情展示
- **EntryProcess页面view模式**：复用进场办理页面，以只读模式展示
- 默认使用专用详情页面，提供更好的用户体验

### 4. EntryProcess支持的模式
- `create` - 新建进场单模式
- `edit` - 编辑进场单模式  
- `view` - 只读查看模式（新增）

### 5. API使用
使用现有的 `getEnterDetail(enterId)` 接口获取详情数据，支持：
- 基本进场单信息
- 合同信息
- 房源列表及详细信息
- 资产配套信息

### 6. 数据结构适配
- 兼容不同接口返回的数据格式
- 智能判断状态（待办理/已办理/部分办理）
- 字典数据转换（合同用途等）

## 路由配置
```
路径: /entry-detail
组件: EntryDetail.vue
参数: 
  - entryId: 进场单ID
  - contractId: 合同ID
  - contractNo: 合同编号
  - tenantName: 承租方名称
```

## 页面布局

### 基本信息卡片
- 白色卡片背景
- 包含合同基本信息和状态
- 蓝色强调色用于状态显示

### 房源信息区域
- 可展开/收起的房源卡片
- 房源基本信息（楼栋、地块、进场日期）
- 配套设施列表
- 水电度数记录
- 备注信息

### 样式特点
- 使用蓝色主题色彩
- 卡片式设计，层次分明
- 移动端优化的字体大小和间距
- 响应式布局适配不同屏幕

## 使用流程
1. 用户在进场管理列表页面点击"查看详情"按钮
2. 页面跳转到进场详情页面，显示加载状态
3. 调用API获取详情数据并展示
4. 用户可查看完整的进场信息
5. 点击返回按钮回到列表页面

## 错误处理
- API调用失败时显示错误提示
- 数据为空时显示空状态页面
- 缺少必要参数时显示提示信息

## 性能优化
- 路由懒加载，按需加载页面组件
- 构建时自动分割代码，减少首屏加载时间
- 合理的加载状态提示，提升用户体验

## 总结
查看详情功能为用户提供了完整的进场信息查看能力，补充了进场管理模块的功能完整性。

### 主要特点
1. **双重实现方案**：提供专用详情页面和复用现有页面两种方式
2. **统一设计风格**：保持与进场办理页面一致的视觉风格
3. **完善的只读支持**：EntryProcess页面的view模式提供完整的只读体验
4. **灵活的扩展性**：可根据需要选择不同的详情展示方式

页面设计简洁美观，交互友好，能够满足用户查看详细信息的各种需求。 