<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getEntryDetail } from '../api/entry'
import { showLoadingToast } from 'vant'

// 获取路由
const router = useRouter()
const route = useRoute()

// 进场通知单详情数据
const entryDetail = ref<any | null>(null)
const loading = ref(false)

// 默认展开的房源ID
const activeNames = ref<string[]>([])

// 获取进场通知单详情
const fetchEntryDetail = async () => {
  try {
    loading.value = true
    const loadingToast = showLoadingToast({
      message: '加载中...',
      forbidClick: true,
    })
    
    // 从路由参数获取enterId，如果没有则使用mock值
    const contractId = (route.query.contractId as string) || '8f7ae4fa63e26e217efa2cb81db77a39'
    
    const response = await getEntryDetail({ contractId })
    entryDetail.value = response.data
    
    loadingToast.close()
  } catch (error) {
    console.error('获取进场通知单详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 页面挂载时获取数据
onMounted(() => {
  fetchEntryDetail()
})
</script>

<template>
    <div class="entrance-notice">
        <!-- 顶部导航栏 -->
        <van-nav-bar title="进场通知单" left-arrow fixed placeholder @click-left="goBack" />

        <!-- 加载状态 -->
        <van-loading v-if="loading" class="loading-wrapper" size="24px" vertical>
            加载中...
        </van-loading>

        <!-- 内容区域 -->
        <div v-else-if="entryDetail" class="notice-content">
            <!-- 合同信息卡片 -->
            <div class="contract-card">
                <div class="contract-info">
                    <div class="info-row">
                        <span class="label">合同号：</span>
                        <span>{{ entryDetail.contract.contractNo }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">承租方：</span>
                        <span>{{ entryDetail.contract.customerName }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">合同周期：</span>
                        <span>{{ entryDetail.contract.startDate }} ～ {{ entryDetail.contract.endDate }}</span>
                    </div>
                </div>
            </div>

            <!-- 房源区域 -->
            <div class="room-section">
                <div class="section-header">
                    <div class="title">
                        <img src="../assets/images/entrance-notice-icon.png" alt="房源图标" class="section-icon" />
                        进场房源 · <span class="room-count">{{ entryDetail.enteredRoomCount }}间</span>
                    </div>
                </div>

                <!-- 房源列表（使用折叠面板） -->
                <van-collapse v-model="activeNames">
                    <van-collapse-item v-for="room in entryDetail.enterRoomList" :key="room.id" :name="room.id">
                        <template #title>
                            <div class="room-name">
                                <img class="room-icon" src="../assets/images/entrance-notice-room-icon.png"
                                    alt="房间图标" />
                                {{ room.parcelName }}-{{ room.buildingName }}-{{ room.roomName }}
                            </div>
                            <span class="entered-tag">进场日期：{{ room.enterDate }}</span>
                        </template>

                        <div class="room-details">
                            <div class="detail-item" v-for="item in room.assetList" :key="item.id">
                                <span class="detail-label">{{ item.name }}{{ !!item.specification ? `(${item.specification})` : '' }}</span>
                                <span class="detail-value">x{{ item.count }}</span>
                            </div>
                        </div>
                    </van-collapse-item>
                </van-collapse>
            </div>
        </div>

        <!-- 数据为空状态 -->
        <van-empty v-else description="暂无进场通知单信息" />

        <!-- 底部提示 -->
        <div v-if="entryDetail" class="notice-footer">
            <div class="footer-content">
                <img src="../assets/icons/reminder-icon.png" alt="提醒图标" class="reminder-icon" />
                <div class="reminder-text">
                    温馨提醒：本次入场房源数 {{ entryDetail.enteredRoomCount }} 间
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.entrance-notice {
    min-height: 100vh;
    background-color: #f1f1f1;
    background-image: url('../assets/images/entrance-notice-bg.png');
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: top center;
    display: flex;
    flex-direction: column;
    padding-bottom: 120px;
}

.loading-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #969799;
}

.notice-content {
    flex: 1;
}

/* 合同信息卡片 */
.contract-card {
    background: linear-gradient(to bottom, #009AFF, #005FFF);
    border-radius: 20px;
    overflow: hidden;
    margin: 30px;
}

.contract-info {
    padding: 30px;
    color: #ffffff;
}

.info-row {
    display: flex;
    font-size: 24px;
    margin-bottom: 10px;
    line-height: 1.4;
}

.info-row:last-child {
    margin-bottom: 0;
}

.label {
    font-weight: 400;
    min-width: 120px;
}

/* 房源区域 */
.room-section {
    background-color: #F1F1F1;
    border-radius: 20px 20px 0 0;
    overflow: hidden;
    padding: 30px;
    margin-bottom: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.title {
    font-size: 30px;
    font-weight: 500;
    color: #242433;
    display: flex;
    align-items: center;
}

.section-icon {
    width: 36px;
    height: 36px;
    margin-right: 10px;
}

.room-count {
    color: #1677FF;
    margin-left: 4px;
}

.entered-count {
    color: #52c41a;
    font-size: 24px;
    margin-left: 8px;
}

.room-icon {
    width: 24px;
    height: 24px;
    margin-right: 20px;
    vertical-align: middle;
}

/* 房源名称样式 */
.room-name {
    font-size: 30px;
    font-weight: 500;
    color: #242433;
    display: flex;
    align-items: center;
    gap: 10px;
}

.entered-tag {
    flex-shrink: 0;
    margin-left: auto;
    color: #666;
    font-size: 24px;
}

/* 房间详情 */
.room-details {
    padding: 30px;
    border-top: 1px solid #EEEEEE;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 24px;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-label {
    color: #666;
    width: 50%;
}

.detail-value {
    color: #333;
    flex: 1;
}

.detail-value.price {
    color: #ff6b35;
    font-weight: 500;
}

/* 底部提示 */
.notice-footer {
    position: fixed;
    height: 100px;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    padding: 16px;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
    z-index: 10;
}

.footer-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.reminder-icon {
    width: 32px;
    height: 32px;
}

.reminder-text {
    font-size: 26px;
    color: #6B6B7A;
}

/* 自定义 van-nav-bar 样式 */
:deep(.van-nav-bar .van-icon) {
    color: #333333;
}

/* 自定义Vant组件样式 */
:deep(.van-collapse-item) {
    margin-bottom: 20px;
    border-radius: 20px;
    overflow: hidden;
}

:deep(.van-collapse-item__title) {
    padding: 20px 30px;
}

:deep(.van-collapse-item__content) {
    padding: 0;
}

/* 标签样式 */
:deep(.van-tag) {
    border: none;
}
</style>