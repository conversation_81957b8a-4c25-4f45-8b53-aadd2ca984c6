<script lang="ts">
export default {
    name: 'PropertyHandoverDetail'
}
</script>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import type { PickerOption } from 'vant'
import { getExitAssetsList, getPropertyDetail, savePropertySign, type ExitRoomVo, type ExitRoomAssetsVo, type ExitPropertySignDTO, type ExitRoomResponse, type ExitRoomDetail, type UploadResponse, type AssetItem, type AssetItemWithParsedAttachments } from '../api/exit'
import { getToken } from '../utils/auth'
import SignatureCanvas from '../components/SignatureCanvas.vue'

// 资产项类型定义
interface ExitAsset {
  id: string
  exitId?: string
  exitRoomId?: string
  category: number
  name: string
  specification: string
  count?: number
  isAdd?: boolean
  attachments: string | null
  usageScope: string
  usageScopeList: any
  remark: string
  createByName: string
  updateByName: string
  isDel: boolean
  status?: number // 状态: 1-完好, 2-损坏, 3-丢失
  penalty?: number // 赔偿金额
}

// 资产列表响应类型
interface ExitAssetsResponse {
  data: ExitAsset[]
  code: number
  msg: string
}

const router = useRouter()
const route = useRoute()

// 页面状态
const loading = ref(false)
const activeRoom = ref('')
const checkedRoom = ref<string[]>([])
const expandedRooms = ref<string[]>([]) // 展开的房间列表

// 返回上一页
const onClickLeft = () => {
    router.back()
}

// 页面数据
const handoverData = ref<{
  customerName: string
  roomList: ExitRoomDetail[]
  exitId?: string
}>({
  customerName: '',
  roomList: [],
  exitId: ''
})

// 签字相关
const showSignature = ref(false)
const signatureUrl = ref('')
const signatureKey = ref(0) // 添加 key 用于强制重新渲染组件

// 状态选项
const showStatusPicker = ref(false)
const statusOptions: PickerOption[] = [
    { text: '完好', value: 1 },
    { text: '损坏', value: 2 },
    { text: '丢失', value: 3 }
]

// 房屋其他情况状态选项
const showDoorWindowPicker = ref(false)
const doorWindowOptions: PickerOption[] = [
    { text: '完好', value: 1 },
    { text: '损坏', value: 2 }
]

const showKeyHandoverPicker = ref(false)
const keyHandoverOptions: PickerOption[] = [
    { text: '已交齐', value: 1 },
    { text: '未交齐', value: 2 }
]

const showCleaningPicker = ref(false)
const cleaningOptions: PickerOption[] = [
    { text: '自行打扫完毕、洁净', value: 1 },
    { text: '保洁及垃圾清理收费', value: 2 }
]

// 合同号
const contractNo = ref('')
const exitId = ref('')

// 活动的标签页
const activeTabName = ref('finance')

// 当前确认类型 1-财务确认, 2-工程确认
const confirmType = ref(1)

// 新增资产相关（保留用于资产选择）

// 资产选择相关
const showAssetPicker = ref(false)
const selectedAssets = ref<string[]>([])
const allAssets = ref<AssetItemWithParsedAttachments[]>([])

// 获取资产列表（已废弃，资产数据现在来自房间详情中的exitRoomAssetsList）
const fetchExitAssetsList = async () => {
    // 已废弃：资产数据现在来自房间详情中的exitRoomAssetsList
}

// 获取房间详情
const fetchRoomDetails = async () => {
    if (!exitId.value) return

    try {
        loading.value = true
        const response = await getPropertyDetail(exitId.value, confirmType.value)
        handoverData.value = {
            customerName: response.data.customerName,
            roomList: response.data.roomList,
            exitId: exitId.value
        }
    } catch (error) {
        console.error('获取房间详情失败:', error)
        showToast('获取房间详情失败')
    } finally {
        loading.value = false
    }
}

// 获取所有资产列表
const fetchAllAssets = async () => {
    try {
        loading.value = true
        // 获取当前房间的资产列表
        const response = await getExitAssetsList(exitId.value)
        if (response.code === 200 && response.rows?.length) {
            // 将接口返回的数据转换为我们需要的格式
            allAssets.value = response.rows.map((asset: any) => ({
                ...asset,
                attachments: asset.attachments ? JSON.parse(asset.attachments) : null
            }))
            console.log('资产列表:', allAssets.value)
        } else {
            showToast(response.msg || '获取资产列表失败')
        }
    } catch (error) {
        console.error('获取资产列表失败:', error)
        showToast('获取资产列表失败')
    } finally {
        loading.value = false
    }
}

// 在页面挂载时，从路由参数中获取数据
onMounted(() => {
    if (route.query.contractNo) {
        contractNo.value = route.query.contractNo as string
    }
    if (route.query.tenant) {
        handoverData.value.customerName = route.query.tenant as string
    }
    if (route.query.exitId) {
        exitId.value = route.query.exitId as string
        handoverData.value.exitId = exitId.value
    }

    // 根据query参数设置默认选中的标签页
    if (route.query.tab === 'engineering') {
        activeTabName.value = 'engineering'
        confirmType.value = 2  // 工程确认
    } else if (route.query.unchecked === 'engineering') {
        activeTabName.value = 'engineering'
        confirmType.value = 2  // 工程确认
    } else if (route.query.unchecked === 'finance') {
        activeTabName.value = 'finance'
        confirmType.value = 1  // 财务确认
    } else {
        // 默认财务确认
        confirmType.value = 1
    }

    // 获取数据
    fetchRoomDetails().then(() => {
        // 默认展开第一个房间
        if (handoverData.value.roomList.length > 0) {
            expandedRooms.value = [handoverData.value.roomList[0].id]
        }
    })
    // 只在需要时获取全部资产列表（用于新增选择）
    fetchAllAssets()
})

// 监听标签页变化
const onTabChange = () => {
    // 清空选中状态
    checkedRoom.value = []
    
    // 根据标签页设置确认类型
    if (activeTabName.value === 'finance') {
        confirmType.value = 1  // 财务确认
    } else if (activeTabName.value === 'engineering') {
        confirmType.value = 2  // 工程确认
    }
    
    // 更新数据
    fetchRoomDetails()
}

// 格式化数字为千分位显示
const formatNumber = (num: number | null | undefined): string => {
    if (num === null || num === undefined) return ''
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 格式化日期时间
const formatDateTime = (dateTimeStr: string): string => {
    if (!dateTimeStr) return ''
    try {
        const date = new Date(dateTimeStr)
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    } catch {
        return dateTimeStr
    }
}

// 获取状态文本
const getStatusText = (status: number): string => {
    const statusMap: Record<number, string> = {
        1: '完好',
        2: '损坏', 
        3: '丢失'
    }
    return statusMap[status] || '未知'
}

// 获取门窗状态文本
const getDoorWindowStatusText = (status: number): string => {
    const statusMap: Record<number, string> = {
        1: '完好',
        2: '损坏'
    }
    return statusMap[status] || '请选择'
}

// 获取钥匙交接状态文本
const getKeyHandoverStatusText = (status: number): string => {
    const statusMap: Record<number, string> = {
        1: '已交齐',
        2: '未交齐'
    }
    return statusMap[status] || '请选择'
}

// 获取清洁状态文本
const getCleaningStatusText = (status: number): string => {
    const statusMap: Record<number, string> = {
        1: '自行打扫完毕、洁净',
        2: '保洁及垃圾清理收费'
    }
    return statusMap[status] || '请选择'
}

// 更新资产状态
const selectedAsset = ref<ExitAsset>()
const selectedRoom = ref<ExitRoomVo>()
const handleStatusPicker = (asset: ExitAsset) => {
    showStatusPicker.value = true
    selectedAsset.value = asset
}

const updateAssetStatus = (status: number) => {
    if (selectedAsset.value) {
        selectedAsset.value.status = status
        // 当选择完好时，清空赔偿金
        if (status === 1) {
            selectedAsset.value.penalty = 0
        }
        
        // 同步更新到房间数据中
        handoverData.value.roomList.forEach(room => {
            if (room.exitRoomAssetsList) {
                const asset = room.exitRoomAssetsList.find(a => a.id === selectedAsset.value?.id)
                if (asset) {
                    asset.status = status
                    if (status === 1) {
                        asset.penalty = 0
                    }
                }
            }
        })
    }
}

// 房屋其他情况状态选择处理
const selectedHouseRoom = ref<any>()

const handleDoorWindowStatusPicker = (room: any) => {
    showDoorWindowPicker.value = true
    selectedHouseRoom.value = room
}

const updateDoorWindowStatus = (status: number) => {
    if (selectedHouseRoom.value) {
        selectedHouseRoom.value.doorWindowStatus = status
        // 当选择完好时，清空赔偿金
        if (status === 1) {
            selectedHouseRoom.value.doorWindowPenalty = 0
        }
    }
}

const handleKeyHandoverStatusPicker = (room: any) => {
    showKeyHandoverPicker.value = true
    selectedHouseRoom.value = room
}

const updateKeyHandoverStatus = (status: number) => {
    if (selectedHouseRoom.value) {
        selectedHouseRoom.value.keyHandoverStatus = status
        // 当选择已交齐时，清空赔偿金
        if (status === 1) {
            selectedHouseRoom.value.keyPenalty = 0
        }
    }
}

const handleCleaningStatusPicker = (room: any) => {
    showCleaningPicker.value = true
    selectedHouseRoom.value = room
}

const updateCleaningStatus = (status: number) => {
    if (selectedHouseRoom.value) {
        selectedHouseRoom.value.cleaningStatus = status
        // 当选择自行打扫完毕时，清空费用
        if (status === 1) {
            selectedHouseRoom.value.cleaningPenalty = 0
        }
    }
}

// 更新赔偿金额
const updatePenalty = (room: any, field: string, value: string) => {
    // 移除千分位后转为数字
    const numValue = value.replace(/,/g, '')
    room[field] = numValue ? parseFloat(numValue) : 0
}

// 更新资产赔偿金额
const updateAssetPenalty = (asset: any, value: string) => {
    // 移除千分位后转为数字
    const numValue = value.replace(/,/g, '')
    const penalty = numValue ? parseFloat(numValue) : 0
    asset.penalty = penalty
    
    // 同步更新到房间数据中
    handoverData.value.roomList.forEach(room => {
        if (room.exitRoomAssetsList) {
            const roomAsset = room.exitRoomAssetsList.find(a => a.id === asset.id)
            if (roomAsset) {
                roomAsset.penalty = penalty
            }
        }
    })
}

// 更新水电费数据
const updateUtilityFee = (room: any, field: string, value: string | number) => {
    const numValue = typeof value === 'string' ? value.replace(/,/g, '') : value.toString()
    room[field] = numValue ? parseFloat(numValue) : 0
}

// 计算合计金额
const totalAmount = computed(() => {
    let total = 0
    let debugInfo: any[] = []
    
    handoverData.value.roomList.forEach(room => {
        if (checkedRoom.value.includes(room.id)) {
            let roomTotal = 0
            let roomDebug: any = { roomName: room.roomName, items: [] }
            
            // 水电费欠费（财务部）
            if (activeTabName.value === 'finance') {
                const elecFee = parseFloat(room.elecFee?.toString() || '0') || 0
                const waterFee = parseFloat(room.waterFee?.toString() || '0') || 0
                const pmFee = parseFloat(room.pmFee?.toString() || '0') || 0
                
                if (elecFee > 0) {
                    roomTotal += elecFee
                    roomDebug.items.push({ type: '电费欠费', amount: elecFee })
                }
                if (waterFee > 0) {
                    roomTotal += waterFee
                    roomDebug.items.push({ type: '水费欠费', amount: waterFee })
                }
                if (pmFee > 0) {
                    roomTotal += pmFee
                    roomDebug.items.push({ type: '物业欠费', amount: pmFee })
                }
            }
            
            // 资产赔偿金额（工程部和财务部都需要计算）
            if (room.exitRoomAssetsList && room.exitRoomAssetsList.length > 0) {
                room.exitRoomAssetsList.forEach(asset => {
                    if (asset.status === 2 || asset.status === 3) {
                        const penalty = parseFloat(asset.penalty?.toString() || '0') || 0
                        if (penalty > 0) {
                            roomTotal += penalty
                            roomDebug.items.push({ type: `${asset.name}赔偿`, amount: penalty })
                        }
                    }
                })
            }
            
            // 房屋其他情况赔偿金额（工程部）
            if (activeTabName.value === 'engineering') {
                // 门窗赔偿
                if (room.doorWindowStatus === 2) {
                    const doorWindowPenalty = parseFloat(room.doorWindowPenalty?.toString() || '0') || 0
                    if (doorWindowPenalty > 0) {
                        roomTotal += doorWindowPenalty
                        roomDebug.items.push({ type: '门窗赔偿', amount: doorWindowPenalty })
                    }
                }
                
                // 钥匙赔偿
                if (room.keyHandoverStatus === 2) {
                    const keyPenalty = parseFloat(room.keyPenalty?.toString() || '0') || 0
                    if (keyPenalty > 0) {
                        roomTotal += keyPenalty
                        roomDebug.items.push({ type: '钥匙赔偿', amount: keyPenalty })
                    }
                }
                
                // 清洁费用
                if (room.cleaningStatus === 2) {
                    const cleaningPenalty = parseFloat(room.cleaningPenalty?.toString() || '0') || 0
                    if (cleaningPenalty > 0) {
                        roomTotal += cleaningPenalty
                        roomDebug.items.push({ type: '清洁费用', amount: cleaningPenalty })
                    }
                }
            }
            
            total += roomTotal
            if (roomTotal > 0) {
                roomDebug.total = roomTotal
                debugInfo.push(roomDebug)
            }
        }
    })
    
    // 在开发环境下打印调试信息
    if (import.meta.env.DEV && debugInfo.length > 0) {
        console.log('💰 合计金额计算详情:', {
            总金额: total,
            明细: debugInfo,
            选中房间: checkedRoom.value,
            当前部门: activeTabName.value
        })
    }
    
    return total
})

// 是否全选
const isAllSelected = computed(() => {
    return checkedRoom.value.length === handoverData.value.roomList.length && handoverData.value.roomList.length > 0
})

const checkedRoomChange = () => {
    // 空函数，计算属性会自动更新
}

// 切换选择状态
const toggleAllSelection = () => {
    if (!isAllSelected.value) {
        checkedRoom.value = handoverData.value.roomList.map(room => room.id)
    } else {
        checkedRoom.value = []
    }
}

// 显示签字弹窗
const showSignatureDialog = () => {
    // 防止重复点击
    if (loading.value) return
    
    // 检查是否有选择房间
    if (checkedRoom.value.length === 0) {
        showToast('请至少选择一个房间')
        return
    }

    signatureKey.value++ // 增加 key 值以重置组件
    showSignature.value = true
}

// 上传签名图片
const uploadSignature = async () => {
  // 这里应该实现签名图片的上传逻辑
  // 暂时返回一个模拟的签名数据
  return JSON.stringify({
    fileName: 'signature.png',
    fileUrl: 'http://example.com/signature.png'
  })
}

// 提交物业签字
const submitPropertySign = async (data: ExitPropertySignDTO) => {
  return await savePropertySign(data)
}

// 签字确认
const onSignatureConfirm = async (uploadResponse: UploadResponse) => {
  try {
    loading.value = true
    
    // 构造JSON字符串格式的签名数据
    const signatureJsonString = JSON.stringify({
      fileName: uploadResponse.fileName,
      fileUrl: uploadResponse.fileUrl
    })
    
    signatureUrl.value = signatureJsonString
    showSignature.value = false
    
    // 构造提交数据
    const signData: ExitPropertySignDTO = {
      exitId: handoverData.value.exitId || '',
      exitRoomList: handoverData.value.roomList
        .filter(room => checkedRoom.value.includes(room.id))
        .map(room => {
          const roomData = { ...room }
          
          // 根据确认类型设置不同的确认信息
          if (confirmType.value === 1) {
            roomData.isFinanceConfirmed = true
            roomData.financeConfirmBy = getToken() || ''
            roomData.financeConfirmByName = handoverData.value.customerName
            roomData.financeConfirmTime = new Date().toISOString()
            roomData.financeConfirmSignature = signatureJsonString
          } else {
            roomData.isEngineeringConfirmed = true
            roomData.engineeringConfirmBy = getToken() || ''
            roomData.engineeringConfirmByName = handoverData.value.customerName
            roomData.engineeringConfirmTime = new Date().toISOString()
            roomData.engineeringConfirmSignature = signatureJsonString
          }
          
          return roomData
        }),
        signatureUrl: signatureJsonString,
      signType: confirmType.value,
      userId: getToken() || '1',
      userName: '1'
    }
    
    // 提交签字
    await submitPropertySign(signData)
    
    showToast('签字确认成功')
    
    // 刷新数据
    await fetchRoomDetails()
    
  } catch (error) {
    console.error('签字确认失败:', error)
    showToast('签字确认失败')
  } finally {
    loading.value = false
  }
}

// 取消签字
const onSignatureCancel = () => {
  showSignature.value = false
}

// 兼容旧的确认方法名
const confirmHandover = showSignatureDialog

// 获取当前房间的资产列表
const getCurrentRoomAssets = (roomId: string) => {
    const room = handoverData.value.roomList.find(r => r.id === roomId)
    return room?.exitRoomAssetsList || []
}

// 添加选中的资产
const handleAddSelectedAssets = () => {
    // 检查是否有展开的房间
    if (expandedRooms.value.length === 0) {
        showToast('请先展开要添加配套的房间')
        return
    }

    // 如果有多个展开的房间，提示用户
    if (expandedRooms.value.length > 1) {
        showToast('请只展开一个房间后再添加配套')
        return
    }

    const currentRoomId = expandedRooms.value[0]
    const currentRoom = handoverData.value.roomList.find(r => r.id === currentRoomId)
    if (!currentRoom) {
        showToast('房间信息不存在')
        return
    }

    // 确保 exitRoomAssetsList 存在
    if (!currentRoom.exitRoomAssetsList) {
        currentRoom.exitRoomAssetsList = []
    }

    const existingAssetIds = currentRoom.exitRoomAssetsList.map(a => a.id)
    const newAssets = selectedAssets.value
        .filter(id => !existingAssetIds.includes(id)) // 过滤掉已存在的资产
        .map(id => {
            const asset = allAssets.value.find(a => a.id === id)
            if (asset) {
                return {
                    id: asset.id,
                    exitId: exitId.value,
                    exitRoomId: currentRoomId,
                    category: asset.category,
                    name: asset.name,
                    specification: asset.specification || '',
                    count: 1,
                    status: 1, // 默认完好
                    penalty: 0, // 默认无赔偿
                    isAdd: true, // 标记为新增
                    remark: asset.remark || '',
                    createByName: asset.createByName || '',
                    updateByName: asset.updateByName || '',
                    isDel: false,
                    attachments: null,
                    usageScope: '',
                    usageScopeList: null
                }
            }
            return null
        })
        .filter(Boolean) as any[]

    if (newAssets.length === 0) {
        showToast('所选资产已存在')
        return
    }

    // 添加到房间的资产列表中
    currentRoom.exitRoomAssetsList.push(...newAssets)
    
    showAssetPicker.value = false
    selectedAssets.value = []
    showToast(`成功添加${newAssets.length}个配套`)
}

// 显示资产选择器
const showAssetSelector = (roomId: string) => {
    // 确保只有一个房间展开
    expandedRooms.value = [roomId]
    showAssetPicker.value = true
}

// 检查资产是否已经添加
const isAssetAlreadyAdded = (assetId: string) => {
    // 检查所有房间的资产列表
    return handoverData.value.roomList.some(room => 
        room.exitRoomAssetsList?.some(asset => asset.id === assetId)
    )
}

// 删除资产
const removeAsset = (assetId: string, roomId: string) => {
    const room = handoverData.value.roomList.find(r => r.id === roomId)
    if (room && room.exitRoomAssetsList) {
        const index = room.exitRoomAssetsList.findIndex(a => a.id === assetId)
        if (index > -1) {
            room.exitRoomAssetsList.splice(index, 1)
            showToast('删除成功')
        }
    }
}

// 切换房间展开状态
const toggleRoomExpand = (roomId: string) => {
    const index = expandedRooms.value.indexOf(roomId)
    if (index > -1) {
        expandedRooms.value.splice(index, 1)
    } else {
        expandedRooms.value.push(roomId)
    }
}
</script>

<template>
    <div class="property-handover-detail">
        <!-- 导航栏 -->
        <van-nav-bar title="物业交割单确认" left-arrow fixed placeholder @click-left="onClickLeft" />

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 加载状态 -->
            <van-loading v-if="loading" class="loading-center" size="24px" vertical>
                加载中...
            </van-loading>
            <!-- 承租方信息 -->
            <div class="tenant-info">
                <div class="tenant-info-bar">
                    <div class="tenant-icon">
                        <img src="../assets/images/tenant-icon.svg" alt="承租方" />
                    </div>
                    <div class="tenant-content">
                        <div class="tenant-label">承租方</div>
                        <div class="tenant-name">{{ handoverData.customerName }}</div>
                    </div>
                </div>

                <!-- 部门选择栏 - 使用Vant Tabs组件 -->
                <van-tabs v-model:active="activeTabName" class="department-tabs" animated @change="onTabChange">
                    <van-tab title="综合或财务部" name="finance">
                        <!-- 综合或财务部内容 -->
                    </van-tab>
                    <van-tab title="工程或客服部" name="engineering">
                        <!-- 工程或客服部内容 -->
                    </van-tab>
                </van-tabs>
            </div>

            <!-- 房源列表 -->
            <div class="rooms-container" v-if="!loading">
                <!-- 房源项 -->
                <van-checkbox-group v-model="checkedRoom" @change="checkedRoomChange">
                    <div v-for="room in handoverData.roomList" :key="room.id" class="room-card">
                        <!-- 房间头部 -->
                        <div class="room-header" @click="toggleRoomExpand(room.id)">
                            <van-checkbox :name="room.id" @click.stop />
                            <div class="room-info">
                                <div class="room-name-row">
                                    <img class="house-icon" src="../assets/images/house-icon.svg" alt="房源" />
                                    <span class="room-name">{{ room.roomName }}</span>
                                </div>
                                <div class="room-date">出场日期：{{ room.exitDate?.split('T')[0] }}</div>
                            </div>
                            <van-icon 
                                :name="expandedRooms.includes(room.id) ? 'arrow-up' : 'arrow-down'" 
                                class="expand-icon" 
                            />
                        </div>

                        <!-- 房间详情 - 可展开收起 -->
                        <div v-if="expandedRooms.includes(room.id)" class="room-content">
                            <van-form>
                                <!-- 水电费情况（综合或财务部专用） -->
                                <div v-if="activeTabName === 'finance'" class="form-section">
                                    <van-field 
                                        label="电表度数" 
                                        placeholder="请输入电表度数" 
                                        input-align="right"
                                        label-width="140px" 
                                        type="number"
                                        v-model="room.elecMeterReading"
                                        @update:model-value="(value) => updateUtilityFee(room, 'elecMeterReading', value)">
                                        <template #button>
                                            <span class="unit">度</span>
                                        </template>
                                    </van-field>

                                    <van-field 
                                        label="电费欠费" 
                                        placeholder="请输入电费欠费" 
                                        input-align="right"
                                        label-width="140px" 
                                        type="number"
                                        v-model="room.elecFee"
                                        @update:model-value="(value) => updateUtilityFee(room, 'elecFee', value)">
                                        <template #button>
                                            <span class="unit">元</span>
                                        </template>
                                    </van-field>
                                    
                                    <van-field 
                                        label="冷水表度数" 
                                        placeholder="请输入冷水表度数" 
                                        input-align="right"
                                        label-width="140px" 
                                        type="number"
                                        v-model="room.coldWaterReading"
                                        @update:model-value="(value) => updateUtilityFee(room, 'coldWaterReading', value)">
                                        <template #button>
                                            <span class="unit">吨</span>
                                        </template>
                                    </van-field>
                                    
                                    <van-field 
                                        label="热水表度数" 
                                        placeholder="请输入热水表度数" 
                                        input-align="right"
                                        label-width="140px" 
                                        type="number"
                                        v-model="room.hotWaterReading"
                                        @update:model-value="(value) => updateUtilityFee(room, 'hotWaterReading', value)">
                                        <template #button>
                                            <span class="unit">吨</span>
                                        </template>
                                    </van-field>
                                    
                                    <van-field 
                                        label="水费欠费" 
                                        placeholder="请输入水费欠费" 
                                        input-align="right"
                                        label-width="140px" 
                                        type="number"
                                        v-model="room.waterFee"
                                        @update:model-value="(value) => updateUtilityFee(room, 'waterFee', value)">
                                        <template #button>
                                            <span class="unit">元</span>
                                        </template>
                                    </van-field>
                                    
                                    <van-field 
                                        label="物业欠费" 
                                        placeholder="请输入物业欠费" 
                                        input-align="right"
                                        label-width="140px" 
                                        type="number"
                                        v-model="room.pmFee"
                                        @update:model-value="(value) => updateUtilityFee(room, 'pmFee', value)">
                                        <template #button>
                                            <span class="unit">元</span>
                                        </template>
                                    </van-field>
                                </div>

                                <!-- 工程或客服部确认区域 -->
                                <div v-if="activeTabName === 'engineering'" class="form-section">
                                    <!-- 房屋其他情况 -->
                                    <div class="other-situation-section">
                                        <div class="section-title-blue">房屋其他情况</div>
                                        
                                        <!-- 门、窗、墙体及其他 -->
                                        <van-field 
                                            label="门、窗、墙体及其他" 
                                            readonly 
                                            is-link 
                                            input-align="right"
                                            label-width="140px" 
                                            :model-value="getDoorWindowStatusText(room.doorWindowStatus)" 
                                            placeholder="请选择"
                                            @click="handleDoorWindowStatusPicker(room)" />
                                        <van-field 
                                            v-if="room.doorWindowStatus === 2" 
                                            label="赔偿金"
                                            placeholder="请输入" 
                                            input-align="right"
                                            label-width="140px"
                                            type="number"
                                            v-model="room.doorWindowPenalty"
                                            @update:model-value="(value) => updatePenalty(room, 'doorWindowPenalty', value)">
                                            <template #button>
                                                <span class="unit">元</span>
                                            </template>
                                        </van-field>

                                        <!-- 钥匙交接 -->
                                        <van-field 
                                            label="钥匙交接" 
                                            readonly 
                                            is-link 
                                            input-align="right"
                                            label-width="140px" 
                                            :model-value="getKeyHandoverStatusText(room.keyHandoverStatus)" 
                                            placeholder="请选择"
                                            @click="handleKeyHandoverStatusPicker(room)" />
                                        <van-field 
                                            v-if="room.keyHandoverStatus === 2" 
                                            label="赔偿金"
                                            placeholder="请输入" 
                                            input-align="right"
                                            label-width="140px"
                                            type="number"
                                            v-model="room.keyPenalty"
                                            @update:model-value="(value) => updatePenalty(room, 'keyPenalty', value)">
                                            <template #button>
                                                <span class="unit">元</span>
                                            </template>
                                        </van-field>

                                        <!-- 清洁卫生 -->
                                        <van-field 
                                            label="清洁卫生" 
                                            readonly 
                                            is-link 
                                            input-align="right"
                                            label-width="140px" 
                                            :model-value="getCleaningStatusText(room.cleaningStatus)" 
                                            placeholder="请选择"
                                            @click="handleCleaningStatusPicker(room)" />
                                        <van-field 
                                            v-if="room.cleaningStatus === 2" 
                                            label="保洁及垃圾清理费"
                                            placeholder="请输入" 
                                            input-align="right"
                                            label-width="140px"
                                            type="number"
                                            v-model="room.cleaningPenalty"
                                            @update:model-value="(value) => updatePenalty(room, 'cleaningPenalty', value)">
                                            <template #button>
                                                <span class="unit">元</span>
                                            </template>
                                        </van-field>
                                    </div>
                                    
                                    <!-- 房间配套情况 -->
                                    <div class="assets-section">
                                        <div class="section-title-blue">
                                            房间配套情况
                                            <van-button 
                                                type="primary" 
                                                size="mini"
                                                @click="showAssetSelector(room.id)"
                                                class="add-btn"
                                            >
                                                + 添加
                                            </van-button>
                                        </div>
                                        
                                        <div v-if="!room.exitRoomAssetsList || room.exitRoomAssetsList.length === 0" class="empty-assets">
                                            暂无配套设施
                                        </div>
                                        
                                        <div v-for="(asset, index) in room.exitRoomAssetsList" :key="asset.id" class="asset-item">
                                            <van-field
                                                input-align="right"
                                                label-width="140px" 
                                                :model-value="getStatusText(asset.status || 1)" 
                                                placeholder="请选择"
                                                readonly 
                                                is-link
                                                @click="handleStatusPicker(asset)">
                                                <template #label>
                                                    <div class="asset-label-container">
                                                        <van-icon 
                                                            v-if="asset.isAdd" 
                                                            name="delete-o" 
                                                            class="delete-icon"
                                                            @click.stop="removeAsset(asset.id, room.id)"
                                                        />
                                                        <span class="asset-name">{{ asset.name }}{{ asset.specification ? `(${asset.specification})` : '' }}</span>
                                                    </div>
                                                </template>
                                            </van-field>
                                            
                                            <van-field 
                                                v-if="asset.status === 2 || asset.status === 3" 
                                                label="赔偿金"
                                                placeholder="请输入" 
                                                input-align="right"
                                                label-width="140px"
                                                type="number"
                                                v-model="asset.penalty"
                                                @update:model-value="(value) => updateAssetPenalty(asset, value)">
                                                <template #button>
                                                    <span class="unit">元</span>
                                                </template>
                                            </van-field>
                                        </div>
                                    </div>
                                </div>
                            </van-form>
                        </div>
                    </div>
                </van-checkbox-group>
            </div>
        </div>

        <van-popup v-model:show="showStatusPicker" position="bottom">
            <van-picker :columns="statusOptions"
                :default-index="statusOptions.findIndex(opt => opt.value === selectedAsset?.status)" 
                @confirm="(params) => {
                    updateAssetStatus(params.selectedValues[0])
                    showStatusPicker = false
                }" 
                @cancel="showStatusPicker = false" 
                title="状态选择" 
                show-toolbar />
        </van-popup>

        <!-- 门窗状态选择器 -->
        <van-popup v-model:show="showDoorWindowPicker" position="bottom">
            <van-picker :columns="doorWindowOptions"
                :default-index="doorWindowOptions.findIndex(opt => opt.value === selectedHouseRoom?.doorWindowStatus)" 
                @confirm="(params) => {
                    updateDoorWindowStatus(params.selectedValues[0])
                    showDoorWindowPicker = false
                }" 
                @cancel="showDoorWindowPicker = false" 
                title="门、窗、墙体及其他状态" 
                show-toolbar />
        </van-popup>

        <!-- 钥匙交接状态选择器 -->
        <van-popup v-model:show="showKeyHandoverPicker" position="bottom">
            <van-picker :columns="keyHandoverOptions"
                :default-index="keyHandoverOptions.findIndex(opt => opt.value === selectedHouseRoom?.keyHandoverStatus)" 
                @confirm="(params) => {
                    updateKeyHandoverStatus(params.selectedValues[0])
                    showKeyHandoverPicker = false
                }" 
                @cancel="showKeyHandoverPicker = false" 
                title="钥匙交接状态" 
                show-toolbar />
        </van-popup>

        <!-- 清洁状态选择器 -->
        <van-popup v-model:show="showCleaningPicker" position="bottom">
            <van-picker :columns="cleaningOptions"
                :default-index="cleaningOptions.findIndex(opt => opt.value === selectedHouseRoom?.cleaningStatus)" 
                @confirm="(params) => {
                    updateCleaningStatus(params.selectedValues[0])
                    showCleaningPicker = false
                }" 
                @cancel="showCleaningPicker = false" 
                title="清洁卫生状态" 
                show-toolbar />
        </van-popup>

        <!-- 签字弹窗 -->
        <van-popup 
            v-model:show="showSignature" 
            position="bottom" 
            :style="{ height: '50%' }"
            round
            closeable
            close-icon-position="top-right"
            @closed="onSignatureCancel"
        >
            <div class="signature-popup">
                <div class="signature-header">
                    <h3>请签字确认</h3>
                    <p>签字后将确认物业交割信息</p>
                </div>
                <SignatureCanvas 
                    :key="signatureKey"
                    @confirm="onSignatureConfirm"
                    @cancel="onSignatureCancel"
                />
            </div>
        </van-popup>

        <!-- 底部操作栏 -->
        <div class="bottom-action-bar">
            <div class="action-bar-content">
                <div class="selection-area">
                    <!-- <div class="checkbox-wrapper" @click="toggleAllSelection">
                        <div class="checkbox" :class="{ 'checked': isAllSelected }"></div>
                        <span class="checkbox-label">全选</span>
                    </div> -->
                    <van-checkbox v-model="isAllSelected" @click="toggleAllSelection">全选</van-checkbox>

                    <div class="total-amount">
                        <span class="total-label">合计</span>
                        <span class="amount">¥{{ formatNumber(totalAmount) }}</span>
                    </div>
                </div>

                <div class="confirm-button" :class="{ disabled: loading || checkedRoom.length === 0 }" @click="confirmHandover">
                    <span>{{ loading ? '处理中...' : '签字确认' }}</span>
                </div>
            </div>
        </div>

        <!-- 资产选择弹窗 -->
        <van-popup
            v-model:show="showAssetPicker"
            position="bottom"
            round
            closeable
            close-icon-position="top-right"
            :style="{ height: '70%' }"
        >
            <div class="asset-picker-popup">
                <div class="popup-header">选择配套</div>
                <div class="popup-content">
                    <van-checkbox-group v-model="selectedAssets">
                        <div class="asset-picker-list">
                            <div v-for="asset in allAssets" :key="asset.id" class="asset-picker-item">
                                <van-checkbox 
                                    :name="asset.id"
                                    :disabled="isAssetAlreadyAdded(asset.id)"
                                >
                                    <div class="asset-info">
                                        <div class="asset-name">{{ asset.name }}</div>
                                        <div v-if="asset.specification" class="asset-spec">({{ asset.specification }})</div>
                                    </div>
                                </van-checkbox>
                            </div>
                        </div>
                    </van-checkbox-group>
                </div>
                <div class="popup-footer">
                    <van-button 
                        round 
                        block 
                        type="primary" 
                        color="#3583FF"
                        @click="handleAddSelectedAssets" 
                        :disabled="selectedAssets.length === 0"
                    >
                        确认 ({{ selectedAssets.length }})
                    </van-button>
                </div>
            </div>
        </van-popup>
    </div>
</template>

<style scoped>
.property-handover-detail {
    min-height: 100vh;
    background-color: #F1F1F1;
    display: flex;
    flex-direction: column;
}

/* 内容区域 */
.content-area {
    flex: 1;
    margin-bottom: 120px;
}

/* 承租方信息 */
.tenant-info {
    background: linear-gradient(to right, #314FFF, #1677FF);
    padding: 30px;
    position: relative;
}

.tenant-info-bar {
    display: flex;
    align-items: center;
    padding: 20px 30px;
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 45px;
}

.tenant-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.tenant-icon img {
    width: 40px;
    height: 40px;
}

.tenant-content {
    display: flex;
    flex-direction: column;
}

.tenant-label {
    color: #B6D2FF;
    font-size: 24px;
    font-weight: 400;
    line-height: 1.4em;
    margin-bottom: 4px;
}

.tenant-name {
    color: #FFFFFF;
    font-size: 28px;
    font-weight: 600;
    line-height: 1.4em;
}

/* Vant Tabs自定义样式 */
:deep(.van-tabs) {
    margin-top: 30px;
}

:deep(.van-tabs__line) {
    background-color: #FFFFFF;
}

:deep(.van-tabs__nav) {
    background-color: transparent;
}

:deep(.van-tab) {
    color: #FFFFFF;
    font-size: 28px;
    font-weight: 600;
    padding-bottom: 10px;
    line-height: 1.4em;
}

:deep(.van-tabs__content) {
    background-color: #F1F1F1;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    margin-top: -20px;
}

/* 房源列表容器 */
.rooms-container {
    padding: 16px;
}

/* 房间卡片 */
.room-card {
    background-color: #FFFFFF;
    border-radius: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

/* 房间头部 */
.room-header {
    display: flex;
    align-items: center;
    padding: 16px;
    cursor: pointer;
    border-bottom: 1px solid #F5F5F5;
}

.room-info {
    flex: 1;
    margin-left: 12px;
}

.room-name-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.house-icon {
    width: 30px;
    height: 30px;
    margin-right: 16px;
}

.room-name {
    font-size: 28px;
    font-weight: 500;
    color: #242433;
}

.room-date {
    font-size: 24px;
    color: #919199;
}

.expand-icon {
    color: #C8C9CC;
    font-size: 16px;
}

/* 房间内容 */
.room-content {
    padding: 0 16px 16px;
}

.form-section {
    margin-bottom: 16px;
}

/* 蓝色标题样式 */
.section-title-blue {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size:28px;
    font-weight: 500;
    color: #1677FF;
    /* margin-bottom: 16px; */
    padding: 22px 0 22px 16px;
    border-bottom: 1px solid #E8EBFF;
}

/* 添加按钮样式 */
.add-btn {
    /* font-size: 24px;
    padding: 4px 8px;
    height: 48px;
    line-height: 48px;
    border-radius: 4px;
    background-color: #3583FF;
    color: #FFFFFF; */
    height: 60px !important;
    font-size: 28px !important;
    font-weight: normal !important;
    padding: 0 12px !important;
}
.add-btn::before{
    

}

/* 空状态样式 */
.empty-assets {
    text-align: center;
    color: #C8C9CC;
    font-size: 14px;
    padding: 20px 0;
}

/* 资产项目样式 */
.asset-item {
    margin-bottom: 8px;
}

/* 其他情况区域 */
.other-situation-section {
    margin-bottom: 16px;
}

/* 底部操作栏 */
.bottom-action-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #FFFFFF;
    box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.04);
    z-index: 10;
}

.action-bar-content {
    padding: 20px 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.selection-area {
    display: flex;
    align-items: center;
}

/* 调整全选字号 */
:deep(.van-checkbox__label) {
    font-size: 24px;
    margin-right: 15px;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    margin-right: 30px;
}

.checkbox {
    width: 30px;
    height: 30px;
    border: 2px solid #B8B8B8;
    border-radius: 15px;
    margin-right: 10px;
    position: relative;
}

.checkbox.checked {
    border-color: #3583FF;
    background-color: #3583FF;
}

.checkbox.checked::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 15px;
    height: 15px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 50%;
}

.checkbox-label {
    font-size: 26px;
    color: #242433;
    font-weight: 500;
    line-height: 1.4em;
}

.total-amount {
    display: flex;
    align-items: center;
}

.total-label {
    font-size: 26px;
    color: #242433;
    font-weight: 500;
    line-height: 1.4em;
    margin-right: 15px;
}

.total-amount .amount {
    font-size: 34px;
    font-weight: 500;
    line-height: 1.4em;
    color: #FF6900;
}

.confirm-button {
    background-color: #3583FF;
    color: #FFFFFF;
    font-size: 34px;
    font-weight: 500;
    padding: 10px 40px;
    border-radius: 45px;
    text-align: center;
    line-height: 1.4em;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.confirm-button.disabled {
    background-color: #CCCCCC;
    cursor: not-allowed;
}

/* 自定义 van-nav-bar 样式 */
:deep(.van-nav-bar .van-icon) {
    color: #333333;
}

/* 加载状态 */
.loading-center {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    flex-direction: column;
}

/* 签字弹窗样式 */
.signature-popup {
    padding: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.signature-header {
    text-align: center;
    margin-bottom: 30px;
}

.signature-header h3 {
    font-size: 32px;
    font-weight: 600;
    color: #242433;
    margin: 0 0 15px 0;
    line-height: 1.4em;
}

.signature-header p {
    font-size: 26px;
    color: #919199;
    margin: 0;
    line-height: 1.4em;
}

/* 水电费情况区域样式 */
.water-electric-section {
    background: #F8F9FF;
    border: 1px solid #E8EBFF;
}

/* 工程部确认区域样式 */
.engineering-section {
    background: #F0F8F0;
    border: 1px solid #D4E6D4;
}

.section-title {
    color: #FFFFFF;
    font-size: 28px;
    font-weight: 600;
    padding: 20px 30px;
    margin: -1px -1px 20px -1px;
    line-height: 1.4em;
}

.water-electric-section .section-title {
    background: #3583FF;
}

.engineering-section .section-title {
    background: #52C41A;
}

/* 资产列表样式 */
.assets-section {
    margin-top: 30px;
    border-top: 1px solid #E8EBFF;
    padding-top: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 ;
    margin-bottom: 20px;
}

.add-asset-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 16px;
    background-color: #3583FF;
    color: #FFFFFF;
    border-radius: 20px;
    font-size: 24px;
    cursor: pointer;
}

.add-asset-btn .van-icon {
    font-size: 24px;
}

.section-subtitle {
    font-size: 28px;
    font-weight: 600;
    color: #242433;
    margin-bottom: 20px;
    padding: 0 30px;
}

.assets-list {
    padding: 0 ;
}

.asset-images {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.asset-remark {
    margin-top: 15px;
    font-size: 24px;
    color: #919199;
    line-height: 1.4;
}

.remark-label {
    color: #242433;
    font-weight: 500;
}

.remark-content {
    color: #919199;
}

/* 资产选择弹窗样式 */
.asset-picker-popup {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
}

.popup-header {
    height: 88px;
    line-height: 88px;
    text-align: center;
    font-size: 32px;
    font-weight: 600;
    color: #242433;
    border-bottom: 1px solid #E8EBFF;
    position: relative;
}

.popup-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.asset-picker-list {
    padding: 0 32px;
}

.asset-picker-item {
    padding: 24px 0;
    border-bottom: 1px solid #E8EBFF;
}

.asset-picker-item:last-child {
    border-bottom: none;
}

.asset-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.asset-name {
    font-size: 28px;
    color: #242433;
}

.asset-spec {
    font-size: 24px;
    color: #919199;
}

.popup-footer {
    padding: 20px 32px;
    border-top: 1px solid #E8EBFF;
    background-color: #FFFFFF;
}

:deep(.van-checkbox__label) {
    flex: 1;
}

:deep(.van-checkbox) {
    align-items: center;
    padding: 0 16px;
}

:deep(.van-checkbox__icon) {
    height: 36px;
    width: 36px;
    border-radius: 36px !important;
}

:deep(.van-checkbox__icon--checked) {
    background-color: #3583FF;
    border-color: #3583FF;
}

:deep(.van-button--primary) {
    height: 88px;
    line-height: 88px;
    font-size: 32px;
}

:deep(.van-popup) {
    max-height: 85vh;
}

:deep(.van-checkbox--disabled) {
    opacity: 0.6;
}

/* 资产标签容器样式 */
.asset-label-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.delete-icon {
    color: #FF4D4F;
    font-size: 30px;
    cursor: pointer;
    /* padding: 4px; */
    border-radius: 50%;
    transition: all 0.3s ease;
}

.delete-icon:hover {
    background-color: #FFF2F0;
    color: #FF7875;
}

.delete-icon:active {
    background-color: #FFE7E7;
    transform: scale(0.95);
}

.asset-name {
    font-size: 28px;
    color: #242433;
    line-height: 1.4em;
}
</style>