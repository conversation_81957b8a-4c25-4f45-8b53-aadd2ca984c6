# 进场管理页面 (EntryManagement.vue)

## 页面概述

进场管理页面是一个用于查看和管理进场通知单的列表页面，用户可以：

- 查看待办理和已办理的进场通知单
- 搜索楼栋/房源/承租方
- 筛选时间范围（近3天、近7天、近30天、全部）
- 进行更多筛选（进场日期、创建日期、合同编号、创建人）
- 办理进场操作

## 功能特性

### 1. 搜索功能
- 支持按楼栋、房源、承租方进行搜索
- 实时搜索，输入后点击搜索按钮

### 2. 状态筛选
- **待办理**：显示未完成进场的通知单
- **已办理**：显示已完成进场的通知单

### 3. 时间筛选
- 近3天（默认）
- 近7天
- 近30天
- 全部

### 4. 排序选项
- 最新创建（默认）
- 进场日期
- 合同编号

### 5. 更多筛选
通过弹框提供详细筛选选项：
- 进场日期区间
- 创建日期区间
- 合同编号
- 创建人

### 6. 操作功能
- **办理进场**：点击按钮跳转到进场通知单页面进行办理
- **查看详情**：点击列表项查看进场通知单详情

## 数据结构

### 列表项显示信息
- 承租方名称（标题）
- 状态（待办理/已办理）
- 合同编号
- 租期（进场日期）
- 未进场房源数

### API接口
- `getEntryList`: 获取进场管理列表
- `handleEntry`: 办理进场操作

## 页面路由

路径：`/entry-management`
路由名：`EntryManagement`

## 使用示例

```typescript
// 跳转到进场管理页面
router.push({ name: 'EntryManagement' })

// 或使用路径跳转
router.push('/entry-management')
```

## 样式特点

- 采用Card式设计，每个进场通知单独立显示
- 蓝色渐变标题栏显示承租方名称和状态
- 响应式设计，适配移动端
- 与定单列表页面保持一致的设计风格

## 相关页面

- **进场通知单页面** (`EntranceNotice.vue`): 办理进场的详情页面
- **定单列表页面** (`BookingList.vue`): 参考的设计模板

## 注意事项

1. 页面需要用户登录状态
2. 默认项目ID为 '108'，可根据实际需求调整
3. 状态码：0-待办理，1-已办理
4. 时间筛选基于创建时间进行过滤 