---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心前端/房源

<a id="opIdgetSimpleDiagram"></a>

## POST 房态简图接口

POST /room/simple/diagram

根据入参查询房源信息，生成房态简图数据，包含楼层名称和房间房态信息

> Body 请求参数

```json
{
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "floorId": "string",
  "roomStatus": 0,
  "propertyType": "string",
  "diagramDate": "2019-08-24T14:15:22Z",
  "dueSoon": true,
  "isSelfUse": true,
  "isLock": true,
  "isDirty": true,
  "isMaintain": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RoomDiagramQueryDTO](#schemaroomdiagramquerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"totalCount":0,"emptyCount":0,"rentCount":0,"toEffectCount":0,"invalidCount":0,"floorDiagramList":[{"floorId":"string","floorName":"string","floorSort":0,"rooms":[{"roomId":"string","roomName":"string","type":0,"propertyType":"string","propertyTypeName":"string","rentArea":0,"orientation":"string","orientationName":"string","isSelfUse":true,"needCheckIn":true,"needCheckOut":true,"roomStatusName":"string","roomStatus":0,"tags":["string"],"houseTypeId":"string","tablePrice":0,"priceUnit":0,"baseRent":0,"additionalFee":0,"rentAreaType":0,"emptyDays":0,"selfUseSubject":0,"rentalStartDate":"2019-08-24T14:15:22Z","externalRentStartDate":"2019-08-24T14:15:22Z","isDirty":true,"isLock":true,"isMaintain":true,"bookingVo":{"bookingId":null,"customerName":null,"companyName":null,"bookingAmount":null,"bookingTime":null,"canRefund":null,"roomStatus":null},"bookings":[{}],"contractVo":{"roomId":null,"contractId":null,"contractNo":null,"contractNumber":null,"contractCategory":null,"contractType":null,"signType":null,"rentPrice":null,"tenantType":null,"tenantName":null,"tenantIdCard":null,"monthlyPrice":null,"startDate":null,"endDate":null,"rentTerm":null,"status":null,"roomStatus":null,"approveStatus":null,"terminateId":null},"contracts":[{}]}]}],"propertyList":["string"]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[DiagramVo](#schemadiagramvo)|

<a id="opIdgetRoomTree"></a>

## POST 查询用户有权限的房间树

POST /room/roomOptions

返回项目-地块-楼栋-房间层级树结构

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "contractType": "string",
  "buildingType": "string",
  "buildingId": "string",
  "rentStatus": "string",
  "roomName": "string",
  "projectId": "string",
  "pricingFlag": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[RoomTreeQueryDTO](#schemaroomtreequerydto)| 否 |none|

> 返回示例

> 200 Response

```
[{"id":"string","name":"string","parentId":"string","roomId":"string","roomName":"string","projectId":"string","projectName":"string","stageId":"string","stageName":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","propertyType":"string","rentStatus":"string","rentAreaType":0,"rentArea":0,"price":0,"bottomPrice":0,"priceUnit":0,"externalRentStartDate":"2019-08-24T14:15:22Z","depositType":0,"depositAmount":0,"level":0,"children":[{"id":"string","name":"string","parentId":"string","roomId":"string","roomName":"string","projectId":"string","projectName":"string","stageId":"string","stageName":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","propertyType":"string","rentStatus":"string","rentAreaType":0,"rentArea":0,"price":0,"bottomPrice":0,"priceUnit":0,"externalRentStartDate":"2019-08-24T14:15:22Z","depositType":0,"depositAmount":0,"level":0,"children":[{"id":"string","name":"string","parentId":"string","roomId":"string","roomName":"string","projectId":"string","projectName":"string","stageId":"string","stageName":"string","parcelId":"string","parcelName":"string","buildingId":"string","buildingName":"string","floorId":"string","floorName":"string","propertyType":"string","rentStatus":"string","rentAreaType":0,"rentArea":0,"price":0,"bottomPrice":0,"priceUnit":0,"externalRentStartDate":"2019-08-24T14:15:22Z","depositType":0,"depositAmount":0,"level":0,"children":[null]}]}]}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[RoomTreeVo](#schemaroomtreevo)]|false|none||none|
|» id|string|false|none|树结构id|none|
|» name|string|false|none|树结构名称|none|
|» parentId|string|false|none|父级id|none|
|» roomId|string|false|none|房源roomId|none|
|» roomName|string|false|none|房源名称|none|
|» projectId|string|false|none|项目id|none|
|» projectName|string|false|none|项目名称|none|
|» stageId|string|false|none|分期id|none|
|» stageName|string|false|none|分期名称|none|
|» parcelId|string|false|none|地块id|none|
|» parcelName|string|false|none|地块名称|none|
|» buildingId|string|false|none|楼栋id|none|
|» buildingName|string|false|none|楼栋名称|none|
|» floorId|string|false|none|楼层id|none|
|» floorName|string|false|none|楼层名称|none|
|» propertyType|string|false|none|物业类型|none|
|» rentStatus|string|false|none|租控状态 0-可租 1-已租（字段待确认）|none|
|» rentAreaType|integer(int32)|false|none|计租面积类型（1建筑面积 2套内面积）|none|
|» rentArea|number|false|none|计租面积|none|
|» price|number|false|none|单价|none|
|» bottomPrice|number|false|none|底价|none|
|» priceUnit|integer(int32)|false|none|单价单位(1元/平方米/月 2元/月 3元/日)|none|
|» externalRentStartDate|string(date-time)|false|none|对外出租起始日期|none|
|» depositType|integer(int32)|false|none|保证金类型|none|
|» depositAmount|number|false|none|保证金金额|none|
|» level|integer(int32)|false|none| 1：项目 2：地块 3：楼栋 4：房间|none|
|» children|[[RoomTreeVo](#schemaroomtreevo)]|false|none|子节点列表|none|
|»» id|string|false|none|树结构id|none|
|»» name|string|false|none|树结构名称|none|
|»» parentId|string|false|none|父级id|none|
|»» roomId|string|false|none|房源roomId|none|
|»» roomName|string|false|none|房源名称|none|
|»» projectId|string|false|none|项目id|none|
|»» projectName|string|false|none|项目名称|none|
|»» stageId|string|false|none|分期id|none|
|»» stageName|string|false|none|分期名称|none|
|»» parcelId|string|false|none|地块id|none|
|»» parcelName|string|false|none|地块名称|none|
|»» buildingId|string|false|none|楼栋id|none|
|»» buildingName|string|false|none|楼栋名称|none|
|»» floorId|string|false|none|楼层id|none|
|»» floorName|string|false|none|楼层名称|none|
|»» propertyType|string|false|none|物业类型|none|
|»» rentStatus|string|false|none|租控状态 0-可租 1-已租（字段待确认）|none|
|»» rentAreaType|integer(int32)|false|none|计租面积类型（1建筑面积 2套内面积）|none|
|»» rentArea|number|false|none|计租面积|none|
|»» price|number|false|none|单价|none|
|»» bottomPrice|number|false|none|底价|none|
|»» priceUnit|integer(int32)|false|none|单价单位(1元/平方米/月 2元/月 3元/日)|none|
|»» externalRentStartDate|string(date-time)|false|none|对外出租起始日期|none|
|»» depositType|integer(int32)|false|none|保证金类型|none|
|»» depositAmount|number|false|none|保证金金额|none|
|»» level|integer(int32)|false|none| 1：项目 2：地块 3：楼栋 4：房间|none|
|»» children|[[RoomTreeVo](#schemaroomtreevo)]|false|none|子节点列表|none|

# 数据模型

<h2 id="tocS_RoomTreeVo">RoomTreeVo</h2>

<a id="schemaroomtreevo"></a>
<a id="schema_RoomTreeVo"></a>
<a id="tocSroomtreevo"></a>
<a id="tocsroomtreevo"></a>

```json
{
  "id": "string",
  "name": "string",
  "parentId": "string",
  "roomId": "string",
  "roomName": "string",
  "projectId": "string",
  "projectName": "string",
  "stageId": "string",
  "stageName": "string",
  "parcelId": "string",
  "parcelName": "string",
  "buildingId": "string",
  "buildingName": "string",
  "floorId": "string",
  "floorName": "string",
  "propertyType": "string",
  "rentStatus": "string",
  "rentAreaType": 0,
  "rentArea": 0,
  "price": 0,
  "bottomPrice": 0,
  "priceUnit": 0,
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "depositType": 0,
  "depositAmount": 0,
  "level": 0,
  "children": [
    {
      "id": "string",
      "name": "string",
      "parentId": "string",
      "roomId": "string",
      "roomName": "string",
      "projectId": "string",
      "projectName": "string",
      "stageId": "string",
      "stageName": "string",
      "parcelId": "string",
      "parcelName": "string",
      "buildingId": "string",
      "buildingName": "string",
      "floorId": "string",
      "floorName": "string",
      "propertyType": "string",
      "rentStatus": "string",
      "rentAreaType": 0,
      "rentArea": 0,
      "price": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "externalRentStartDate": "2019-08-24T14:15:22Z",
      "depositType": 0,
      "depositAmount": 0,
      "level": 0,
      "children": [
        {
          "id": "string",
          "name": "string",
          "parentId": "string",
          "roomId": "string",
          "roomName": "string",
          "projectId": "string",
          "projectName": "string",
          "stageId": "string",
          "stageName": "string",
          "parcelId": "string",
          "parcelName": "string",
          "buildingId": "string",
          "buildingName": "string",
          "floorId": "string",
          "floorName": "string",
          "propertyType": "string",
          "rentStatus": "string",
          "rentAreaType": 0,
          "rentArea": 0,
          "price": 0,
          "bottomPrice": 0,
          "priceUnit": 0,
          "externalRentStartDate": "2019-08-24T14:15:22Z",
          "depositType": 0,
          "depositAmount": 0,
          "level": 0,
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|树结构id|none|
|name|string|false|none|树结构名称|none|
|parentId|string|false|none|父级id|none|
|roomId|string|false|none|房源roomId|none|
|roomName|string|false|none|房源名称|none|
|projectId|string|false|none|项目id|none|
|projectName|string|false|none|项目名称|none|
|stageId|string|false|none|分期id|none|
|stageName|string|false|none|分期名称|none|
|parcelId|string|false|none|地块id|none|
|parcelName|string|false|none|地块名称|none|
|buildingId|string|false|none|楼栋id|none|
|buildingName|string|false|none|楼栋名称|none|
|floorId|string|false|none|楼层id|none|
|floorName|string|false|none|楼层名称|none|
|propertyType|string|false|none|物业类型|none|
|rentStatus|string|false|none|租控状态 0-可租 1-已租（字段待确认）|none|
|rentAreaType|integer(int32)|false|none|计租面积类型（1建筑面积 2套内面积）|none|
|rentArea|number|false|none|计租面积|none|
|price|number|false|none|单价|none|
|bottomPrice|number|false|none|底价|none|
|priceUnit|integer(int32)|false|none|单价单位(1元/平方米/月 2元/月 3元/日)|none|
|externalRentStartDate|string(date-time)|false|none|对外出租起始日期|none|
|depositType|integer(int32)|false|none|保证金类型|none|
|depositAmount|number|false|none|保证金金额|none|
|level|integer(int32)|false|none| 1：项目 2：地块 3：楼栋 4：房间|none|
|children|[[RoomTreeVo](#schemaroomtreevo)]|false|none|子节点列表|none|

<h2 id="tocS_RoomTreeQueryDTO">RoomTreeQueryDTO</h2>

<a id="schemaroomtreequerydto"></a>
<a id="schema_RoomTreeQueryDTO"></a>
<a id="tocSroomtreequerydto"></a>
<a id="tocsroomtreequerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "contractType": "string",
  "buildingType": "string",
  "buildingId": "string",
  "rentStatus": "string",
  "roomName": "string",
  "projectId": "string",
  "pricingFlag": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|contractType|string|false|none|合同类型，0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型，0-非宿舍,1-宿舍,2-多经,3-日租房|
|buildingType|string|false|none|业态|业态|
|buildingId|string|false|none|楼栋id|楼栋id|
|rentStatus|string|false|none|租控状态 0-可租 1-已租|租控状态 0-可租 1-已租|
|roomName|string|false|none|房源名称|房源名称|
|projectId|string|false|none|项目id|项目id|
|pricingFlag|integer(int32)|false|none|是否定价 0:否，1：是|是否定价 0:否，1：是|

<h2 id="tocS_RoomDiagramQueryDTO">RoomDiagramQueryDTO</h2>

<a id="schemaroomdiagramquerydto"></a>
<a id="schema_RoomDiagramQueryDTO"></a>
<a id="tocSroomdiagramquerydto"></a>
<a id="tocsroomdiagramquerydto"></a>

```json
{
  "projectId": "string",
  "parcelId": "string",
  "buildingId": "string",
  "floorId": "string",
  "roomStatus": 0,
  "propertyType": "string",
  "diagramDate": "2019-08-24T14:15:22Z",
  "dueSoon": true,
  "isSelfUse": true,
  "isLock": true,
  "isDirty": true,
  "isMaintain": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectId|string|false|none|项目ID|项目ID|
|parcelId|string|false|none|地块ID|地块ID|
|buildingId|string|false|none|楼栋ID|楼栋ID|
|floorId|string|false|none|楼层ID|楼层ID|
|roomStatus|integer(int32)|false|none|状态|房源状态(1.空置，2.在租，3.待生效/签约中/已预定， 4.不可招商)|
|propertyType|string|false|none|用途|房源用途|
|diagramDate|string(date-time)|false|none|指定房态时间|指定房态时间，默认为当前时间|
|dueSoon|boolean|false|none|是否即将到期|是否即将到期: true-是，false-否|
|isSelfUse|boolean|false|none|是否自用|是否自用: true-是，false-否|
|isLock|boolean|false|none|是否锁房|是否锁房: true-是，false-否|
|isDirty|boolean|false|none|是否脏房|是否脏房: true-是，false-否|
|isMaintain|boolean|false|none|是否维修|是否维修: true-是，false-否|

<h2 id="tocS_BookDiagramVo">BookDiagramVo</h2>

<a id="schemabookdiagramvo"></a>
<a id="schema_BookDiagramVo"></a>
<a id="tocSbookdiagramvo"></a>
<a id="tocsbookdiagramvo"></a>

```json
{
  "bookingId": "string",
  "customerName": "string",
  "companyName": "string",
  "bookingAmount": 0,
  "bookingTime": "2019-08-24T14:15:22Z",
  "canRefund": true,
  "roomStatus": "string"
}

```

订单信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|bookingId|string|false|none|订单ID|订单ID|
|customerName|string|false|none|客户名称|客户名称|
|companyName|string|false|none|公司名称|公司名称|
|bookingAmount|number|false|none|预定单金额|预定单金额|
|bookingTime|string(date-time)|false|none|预定时间|预定时间|
|canRefund|boolean|false|none|是否可退|是否可退（0否 1是）|
|roomStatus|string|false|none|房间状态|房间状态|

<h2 id="tocS_ContractDiagramVo">ContractDiagramVo</h2>

<a id="schemacontractdiagramvo"></a>
<a id="schema_ContractDiagramVo"></a>
<a id="tocScontractdiagramvo"></a>
<a id="tocscontractdiagramvo"></a>

```json
{
  "roomId": "string",
  "contractId": "string",
  "contractNo": "string",
  "contractNumber": "string",
  "contractCategory": "string",
  "contractType": 0,
  "signType": 0,
  "rentPrice": 0,
  "tenantType": "string",
  "tenantName": "string",
  "tenantIdCard": "string",
  "monthlyPrice": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "rentTerm": "string",
  "status": 0,
  "roomStatus": "string",
  "approveStatus": 0,
  "terminateId": "string"
}

```

合同信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roomId|string|false|none|房间id|房间id|
|contractId|string|false|none|合同id|合同id|
|contractNo|string|false|none|合同号|合同号|
|contractNumber|string|false|none|合同编号|合同编号|
|contractCategory|string|false|none|合同类别|合同类别|
|contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|signType|integer(int32)|false|none|签约类型,0-新签,1-续签|签约类型,0-新签,1-续签|
|rentPrice|number|false|none|租金|租金|
|tenantType|string|false|none|承租类型|承租类型|
|tenantName|string|false|none|承租人|承租人|
|tenantIdCard|string|false|none|承租人证件号|承租人证件号|
|monthlyPrice|number|false|none|签约月总价（元/月）|签约月总价（元/月）|
|startDate|string(date-time)|false|none|合同开始时间|合同开始时间|
|endDate|string(date-time)|false|none|合同结束时间|合同结束时间|
|rentTerm|string|false|none|合同租期|合同租期|
|status|integer(int32)|false|none|合同一级状态|合同一级状态|
|roomStatus|string|false|none|房间状态|房间状态|
|approveStatus|integer(int32)|false|none|审批状态|审批状态|
|terminateId|string|false|none|退租单id|退租单id|

<h2 id="tocS_FloorDiagramVo">FloorDiagramVo</h2>

<a id="schemafloordiagramvo"></a>
<a id="schema_FloorDiagramVo"></a>
<a id="tocSfloordiagramvo"></a>
<a id="tocsfloordiagramvo"></a>

```json
{
  "floorId": "string",
  "floorName": "string",
  "floorSort": 0,
  "rooms": [
    {
      "roomId": "string",
      "roomName": "string",
      "type": 0,
      "propertyType": "string",
      "propertyTypeName": "string",
      "rentArea": 0,
      "orientation": "string",
      "orientationName": "string",
      "isSelfUse": true,
      "needCheckIn": true,
      "needCheckOut": true,
      "roomStatusName": "string",
      "roomStatus": 0,
      "tags": [
        "string"
      ],
      "houseTypeId": "string",
      "tablePrice": 0,
      "priceUnit": 0,
      "baseRent": 0,
      "additionalFee": 0,
      "rentAreaType": 0,
      "emptyDays": 0,
      "selfUseSubject": 0,
      "rentalStartDate": "2019-08-24T14:15:22Z",
      "externalRentStartDate": "2019-08-24T14:15:22Z",
      "isDirty": true,
      "isLock": true,
      "isMaintain": true,
      "bookingVo": {
        "bookingId": "string",
        "customerName": "string",
        "companyName": "string",
        "bookingAmount": 0,
        "bookingTime": "2019-08-24T14:15:22Z",
        "canRefund": true,
        "roomStatus": "string"
      },
      "bookings": [
        {
          "bookingId": "string",
          "customerName": "string",
          "companyName": "string",
          "bookingAmount": 0,
          "bookingTime": "2019-08-24T14:15:22Z",
          "canRefund": true,
          "roomStatus": "string"
        }
      ],
      "contractVo": {
        "roomId": "string",
        "contractId": "string",
        "contractNo": "string",
        "contractNumber": "string",
        "contractCategory": "string",
        "contractType": 0,
        "signType": 0,
        "rentPrice": 0,
        "tenantType": "string",
        "tenantName": "string",
        "tenantIdCard": "string",
        "monthlyPrice": 0,
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z",
        "rentTerm": "string",
        "status": 0,
        "roomStatus": "string",
        "approveStatus": 0,
        "terminateId": "string"
      },
      "contracts": [
        {
          "roomId": "string",
          "contractId": "string",
          "contractNo": "string",
          "contractNumber": "string",
          "contractCategory": "string",
          "contractType": 0,
          "signType": 0,
          "rentPrice": 0,
          "tenantType": "string",
          "tenantName": "string",
          "tenantIdCard": "string",
          "monthlyPrice": 0,
          "startDate": "2019-08-24T14:15:22Z",
          "endDate": "2019-08-24T14:15:22Z",
          "rentTerm": "string",
          "status": 0,
          "roomStatus": "string",
          "approveStatus": 0,
          "terminateId": "string"
        }
      ]
    }
  ]
}

```

楼层房态图

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|floorId|string|false|none|楼层ID|楼层ID|
|floorName|string|false|none|楼层名称|楼层名称|
|floorSort|integer(int32)|false|none|楼层排序|楼层排序|
|rooms|[[RoomDiagramVo](#schemaroomdiagramvo)]|false|none|房间信息列表|该楼层下的房间房态信息列表|

<h2 id="tocS_RoomDiagramVo">RoomDiagramVo</h2>

<a id="schemaroomdiagramvo"></a>
<a id="schema_RoomDiagramVo"></a>
<a id="tocSroomdiagramvo"></a>
<a id="tocsroomdiagramvo"></a>

```json
{
  "roomId": "string",
  "roomName": "string",
  "type": 0,
  "propertyType": "string",
  "propertyTypeName": "string",
  "rentArea": 0,
  "orientation": "string",
  "orientationName": "string",
  "isSelfUse": true,
  "needCheckIn": true,
  "needCheckOut": true,
  "roomStatusName": "string",
  "roomStatus": 0,
  "tags": [
    "string"
  ],
  "houseTypeId": "string",
  "tablePrice": 0,
  "priceUnit": 0,
  "baseRent": 0,
  "additionalFee": 0,
  "rentAreaType": 0,
  "emptyDays": 0,
  "selfUseSubject": 0,
  "rentalStartDate": "2019-08-24T14:15:22Z",
  "externalRentStartDate": "2019-08-24T14:15:22Z",
  "isDirty": true,
  "isLock": true,
  "isMaintain": true,
  "bookingVo": {
    "bookingId": "string",
    "customerName": "string",
    "companyName": "string",
    "bookingAmount": 0,
    "bookingTime": "2019-08-24T14:15:22Z",
    "canRefund": true,
    "roomStatus": "string"
  },
  "bookings": [
    {
      "bookingId": "string",
      "customerName": "string",
      "companyName": "string",
      "bookingAmount": 0,
      "bookingTime": "2019-08-24T14:15:22Z",
      "canRefund": true,
      "roomStatus": "string"
    }
  ],
  "contractVo": {
    "roomId": "string",
    "contractId": "string",
    "contractNo": "string",
    "contractNumber": "string",
    "contractCategory": "string",
    "contractType": 0,
    "signType": 0,
    "rentPrice": 0,
    "tenantType": "string",
    "tenantName": "string",
    "tenantIdCard": "string",
    "monthlyPrice": 0,
    "startDate": "2019-08-24T14:15:22Z",
    "endDate": "2019-08-24T14:15:22Z",
    "rentTerm": "string",
    "status": 0,
    "roomStatus": "string",
    "approveStatus": 0,
    "terminateId": "string"
  },
  "contracts": [
    {
      "roomId": "string",
      "contractId": "string",
      "contractNo": "string",
      "contractNumber": "string",
      "contractCategory": "string",
      "contractType": 0,
      "signType": 0,
      "rentPrice": 0,
      "tenantType": "string",
      "tenantName": "string",
      "tenantIdCard": "string",
      "monthlyPrice": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "rentTerm": "string",
      "status": 0,
      "roomStatus": "string",
      "approveStatus": 0,
      "terminateId": "string"
    }
  ]
}

```

房间信息列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roomId|string|false|none|房间ID|房间ID|
|roomName|string|false|none|房间名称|房间名称|
|type|integer(int32)|false|none|房源类型（1普通 2多经）|房源类型（1普通 2多经）|
|propertyType|string|false|none|用途|物业类型，二级字典|
|propertyTypeName|string|false|none|用途名称|物业类型，二级字典|
|rentArea|number|false|none|计租面积|计租面积|
|orientation|string|false|none|朝向|朝向|
|orientationName|string|false|none|朝向名称|朝向|
|isSelfUse|boolean|false|none|是否自用|是否自用（0否 1是）|
|needCheckIn|boolean|false|none|进场标识|进场标识，true表示未进场，需要进场|
|needCheckOut|boolean|false|none|出场标识|出场标识，true表示需要出场|
|roomStatusName|string|false|none|状态|房间状态|
|roomStatus|integer(int32)|false|none|状态编码|房源状态(1.空置，2.在租，3.待生效/签约中/已预定， 4.不可招商)|
|tags|[string]|false|none|标识数组|房源标识列表，如：已预订、签约中、待生效、未进场、即将到期、未出场|
|» 标识数组|string|false|none|标识数组|房源标识列表，如：已预订、签约中、待生效、未进场、即将到期、未出场|
|houseTypeId|string|false|none|户型|户型ID|
|tablePrice|number|false|none|单价|表价|
|priceUnit|integer(int32)|false|none|计租单位(1元/平方米/月 2元/月 3元/日)|计租单位(1元/平方米/月 2元/月 3元/日)|
|baseRent|number|false|none|基础租金|基础租金|
|additionalFee|number|false|none|附加费用|附加费用|
|rentAreaType|integer(int32)|false|none|计租单位|计租面积类型（1建筑面积 2套内面积）|
|emptyDays|integer(int32)|false|none|空置天数|空置天数|
|selfUseSubject|integer(int32)|false|none|自用主体|自用主体（1运营 2商服 3众创 4其他）|
|rentalStartDate|string(date-time)|false|none|可招商日期|可招商日期|
|externalRentStartDate|string(date-time)|false|none|对外出租起始日期|对外出租起始日期|
|isDirty|boolean|false|none|是否脏房|是否脏房（0否 1是）|
|isLock|boolean|false|none|是否锁房|是否锁房（0否 1是）|
|isMaintain|boolean|false|none|是否维修|是否维修（0否 1是）|
|bookingVo|[BookDiagramVo](#schemabookdiagramvo)|false|none||该房间的订单信息列表|
|bookings|[[BookDiagramVo](#schemabookdiagramvo)]|false|none|订单信息列表|该房间的订单信息列表|
|contractVo|[ContractDiagramVo](#schemacontractdiagramvo)|false|none||该房间的合同信息列表|
|contracts|[[ContractDiagramVo](#schemacontractdiagramvo)]|false|none|合同信息列表|该房间的合同信息列表|

<h2 id="tocS_DiagramVo">DiagramVo</h2>

<a id="schemadiagramvo"></a>
<a id="schema_DiagramVo"></a>
<a id="tocSdiagramvo"></a>
<a id="tocsdiagramvo"></a>

```json
{
  "totalCount": 0,
  "emptyCount": 0,
  "rentCount": 0,
  "toEffectCount": 0,
  "invalidCount": 0,
  "floorDiagramList": [
    {
      "floorId": "string",
      "floorName": "string",
      "floorSort": 0,
      "rooms": [
        {
          "roomId": "string",
          "roomName": "string",
          "type": 0,
          "propertyType": "string",
          "propertyTypeName": "string",
          "rentArea": 0,
          "orientation": "string",
          "orientationName": "string",
          "isSelfUse": true,
          "needCheckIn": true,
          "needCheckOut": true,
          "roomStatusName": "string",
          "roomStatus": 0,
          "tags": [
            "string"
          ],
          "houseTypeId": "string",
          "tablePrice": 0,
          "priceUnit": 0,
          "baseRent": 0,
          "additionalFee": 0,
          "rentAreaType": 0,
          "emptyDays": 0,
          "selfUseSubject": 0,
          "rentalStartDate": "2019-08-24T14:15:22Z",
          "externalRentStartDate": "2019-08-24T14:15:22Z",
          "isDirty": true,
          "isLock": true,
          "isMaintain": true,
          "bookingVo": {
            "bookingId": null,
            "customerName": null,
            "companyName": null,
            "bookingAmount": null,
            "bookingTime": null,
            "canRefund": null,
            "roomStatus": null
          },
          "bookings": [
            {}
          ],
          "contractVo": {
            "roomId": null,
            "contractId": null,
            "contractNo": null,
            "contractNumber": null,
            "contractCategory": null,
            "contractType": null,
            "signType": null,
            "rentPrice": null,
            "tenantType": null,
            "tenantName": null,
            "tenantIdCard": null,
            "monthlyPrice": null,
            "startDate": null,
            "endDate": null,
            "rentTerm": null,
            "status": null,
            "roomStatus": null,
            "approveStatus": null,
            "terminateId": null
          },
          "contracts": [
            {}
          ]
        }
      ]
    }
  ],
  "propertyList": [
    "string"
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|totalCount|integer(int32)|false|none|总数|总数|
|emptyCount|integer(int32)|false|none|空置数|空置数|
|rentCount|integer(int32)|false|none|在租数|在租数|
|toEffectCount|integer(int32)|false|none|待生效/签约中/已预定总数|待生效/签约中/已预定总数|
|invalidCount|integer(int32)|false|none|不可招商数|不可招商数|
|floorDiagramList|[[FloorDiagramVo](#schemafloordiagramvo)]|false|none|楼层房态图|楼层房态图|
|propertyList|[string]|false|none|用途列表|用途列表|
|» 用途列表|string|false|none|用途列表|用途列表|

