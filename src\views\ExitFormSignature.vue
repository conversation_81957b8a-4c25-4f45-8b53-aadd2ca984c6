<script lang="ts">
export default {
    name: 'ExitFormSignature'
}
</script>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showDialog } from 'vant'
import SignatureCanvas from '../components/SignatureCanvas.vue'
import { getExitDetail as fetchExitDetail, saveCustomerSign } from '../api/exit'
import type { ExitDetailVo, UploadResponse } from '../api/exit'

const router = useRouter()
const route = useRoute()

const exitId = route.query.exitId as string
const loading = ref(false)
const showSignature = ref(false)

const signatureKey = ref(0) // 添加 key 用于强制重新渲染组件

// 返回上一页
const onClickLeft = () => {
    router.back()
}

// 合同数据
const contractData = ref({
    contractNo: '',
    tenant: '',
    contractPeriod: '',
    exitDate: ''
})

// 房源列表数据
const houseList = ref<any[]>([])

// 费用结算数据
const settlementData = ref({
    deposit: 0, // 退保证金
    rent: 0,   // 收租金
    compensation: 0, // 收赔偿金
    penalty: 0, // 收罚没金
    discountAmount: 0, // 减免金额
    finalRefund: 0 // 最终应退
})

// 水电物业费数据
const utilityData = ref({
    electric: 0,
    water: 0,
    property: 0
})

// 未完成手续数据
const unfinishedProcedures = ref([
    { name: '营业执照', status: '需注销' },
    { name: '税务登记证', status: '需注销' }
])

// 获取退场单详情
const getExitDetail = async () => {
    try {
        loading.value = true
        const { data } = await fetchExitDetail(exitId)
        console.log(data)

        // 更新合同信息
        const { exitInfo, contractTerminateInfo } = data
        contractData.value = {
            contractNo: exitInfo?.contractNo || '',
            tenant: exitInfo?.customerName || '',
            contractPeriod: contractTerminateInfo?.contract ?
                `${contractTerminateInfo.contract.startDate || ''} ～ ${contractTerminateInfo.contract.endDate || ''}` :
                '',
            exitDate: contractTerminateInfo?.terminateDate || exitInfo?.terminateDate || ''
        }

        // 更新房源列表
        houseList.value = (data.exitRoomList || []).map(room => ({
            location: `${room.parcelName || ''}-${room.buildingName || ''}-${room.roomName || ''}`,
            exitDate: room.exitDate || ''
        }))

        // 更新水电费数据
        const totalElecFee = (data.exitRoomList || []).reduce((sum, room) => sum + (room.elecFee || 0), 0)
        const totalWaterFee = (data.exitRoomList || []).reduce((sum, room) => sum + (room.waterFee || 0), 0)
        const totalPmFee = (data.exitRoomList || []).reduce((sum, room) => sum + (room.pmFee || 0), 0)

        utilityData.value = {
            electric: totalElecFee,
            water: totalWaterFee,
            property: totalPmFee
        }

        // 更新费用结算数据
        const exitCostList = data.exitCostList || []
        let deposit = 0
        let rent = 0
        let compensation = 0
        let penalty = 0

        exitCostList.forEach(cost => {
            if (!cost) return
            switch (cost.type) {
                case 1: // 保证金
                    deposit = cost.amount || 0
                    break
                case 2: // 租金
                    rent = cost.amount || 0
                    break
                case 3: // 赔偿金
                    compensation = cost.amount || 0
                    break
                case 4: // 罚没金
                    penalty = cost.amount || 0
                    break
            }
        })

        settlementData.value = {
            deposit,
            rent,
            compensation,
            penalty,
            discountAmount: exitInfo?.discountAmount || 0,
            finalRefund: exitInfo?.finalAmount || 0
        }

        // 更新未完成手续
        unfinishedProcedures.value = [
            {
                name: '营业执照',
                status: exitInfo?.licenseStatus === 1 ? '未办理执照' :
                    exitInfo?.licenseStatus === 2 ? '需注销' :
                        exitInfo?.licenseStatus === 3 ? '已注销' : '未知'
            },
            {
                name: '税务登记证',
                status: exitInfo?.taxCertStatus === 1 ? '未办理执照' :
                    exitInfo?.taxCertStatus === 2 ? '需注销' :
                        exitInfo?.taxCertStatus === 3 ? '已注销' : '未知'
            }
        ]

    } catch (error: any) {
        showToast({
            message: error.message || '获取退场单详情失败',
            type: 'fail'
        })
    } finally {
        loading.value = false
    }
}

// 处理签字确认
const handleSignConfirm = async (uploadResponse: UploadResponse) => {
    try {
        loading.value = true

        // 构造JSON字符串格式的签名数据
        const signatureJsonString = JSON.stringify({
            fileName: uploadResponse.fileName,
            fileUrl: uploadResponse.fileUrl
        })

        // signatureUrl.value = signatureJsonString

        // 保存客户签字信息
        await saveCustomerSign({
            exitId,
            signatureUrl: signatureJsonString
        })

        showToast({
            message: '签字确认成功',
            type: 'success'
        })

        // 关闭签字弹窗
        showSignature.value = false

        

        // 返回上一页或跳转到成功页面
        // router.back()

    } catch (error: any) {
        showToast({
            message: error.message || '签字确认失败',
            type: 'fail'
        })
    } finally {
        loading.value = false
    }
}

// 签字确认
const confirmSignature = () => {
    showDialog({
        title: '确认提示',
        message: '请确认信息无误后进行签字',
        confirmButtonText: '确认签字',
        cancelButtonText: '取消',
    }).then(() => {
        showSignature.value = true
    }).catch(() => { })
}

onMounted(() => {
    if (!exitId) {
        showToast('退场单ID不能为空')
        router.back()
        return
    }
    getExitDetail()
})
</script>

<template>
    <div class="exit-form-signature">
        <!-- 顶部导航栏 -->
        <van-nav-bar title="退场单签字确认" left-arrow fixed placeholder @click-left="onClickLeft" />

        <van-loading v-if="loading" vertical>加载中...</van-loading>

        <template v-else>
            <!-- 合同信息卡片 -->
            <div class="info-card contract-card">
                <div class="info-item">
                    <span>合同号：{{ contractData.contractNo }}</span>
                </div>
                <div class="info-item">
                    <span>承租方：{{ contractData.tenant }}</span>
                </div>
                <div class="info-item">
                    <span>合同周期：{{ contractData.contractPeriod }}</span>
                </div>
                <div class="info-item">
                    <span>退租日期：{{ contractData.exitDate }}</span>
                </div>
            </div>

            <!-- 房源列表 -->
            <div class="house-list">
                <div v-for="(item, index) in houseList" :key="index" class="house-item">
                    <div class="house-header">
                        <img class="room-icon" src="../assets/images/entrance-notice-room-icon.png" alt="房间图标" />
                        <span class="house-location">{{ item.location }}</span>
                    </div>
                    <div class="house-date">
                        <span>出场日期：{{ item.exitDate }}</span>
                    </div>
                </div>
            </div>

            <!-- 费用结算 -->
            <div class="info-card">
                <div class="card-header">
                    <img src="../assets/images/settlement-icon.svg" alt="费用结算图标" class="header-icon" />
                    <span>费用结算</span>
                </div>

                <div class="settlement-list">
                    <div class="settlement-item">
                        <div class="item-circle refund">退</div>
                        <div class="item-info">
                            <span class="item-name">保证金</span>
                            <span class="item-value">¥{{ settlementData.deposit.toFixed(2) }}</span>
                        </div>
                    </div>

                    <div class="settlement-item">
                        <div class="item-circle collect">收</div>
                        <div class="item-info">
                            <span class="item-name">租金</span>
                            <span class="item-value">¥{{ settlementData.rent.toFixed(2) }}</span>
                        </div>
                    </div>

                    <div class="settlement-item">
                        <div class="item-circle collect">收</div>
                        <div class="item-info">
                            <span class="item-name">赔偿金</span>
                            <span class="item-value">¥{{ settlementData.compensation.toFixed(2) }}</span>
                        </div>
                    </div>

                    <div class="settlement-item">
                        <div class="item-circle collect">收</div>
                        <div class="item-info">
                            <span class="item-name">罚没金</span>
                            <span class="item-value">¥{{ settlementData.penalty.toFixed(2) }}</span>
                        </div>
                    </div>
                    <!-- 减免金额和最终应退 -->
                    <div class="summary-box">
                        <div class="summary-row">
                            <span class="summary-label">减免金额</span>
                            <span class="summary-value">¥{{ settlementData.discountAmount.toFixed(2) }}</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">最终应退</span>
                            <span class="summary-value refund-amount">¥{{ settlementData.finalRefund.toFixed(2)
                                }}</span>
                        </div>
                    </div>
                </div>

            </div>

            <!-- 水电费和物业费情况 -->
            <div class="info-card">
                <div class="card-header">
                    <img src="../assets/images/utility-icon.svg" alt="水电费和物业费图标" class="header-icon" />
                    <span>水电费&物业费情况</span>
                </div>

                <div class="utility-info">
                    <div class="utility-item">
                        <span class="utility-name">电费欠款</span>
                        <span class="utility-value">¥{{ utilityData.electric.toFixed(2) }}</span>
                    </div>

                    <div class="utility-item">
                        <span class="utility-name">水费欠款</span>
                        <span class="utility-value">¥{{ utilityData.water.toFixed(2) }}</span>
                    </div>

                    <div class="utility-item">
                        <span class="utility-name">物业费欠款</span>
                        <span class="utility-value">¥{{ utilityData.property.toFixed(2) }}</span>
                    </div>

                    <div class="utility-warning">
                        <van-icon name="warning-o" color="#E9691F" size="24" />
                        <span>温整提醒：这部分要用请尽快跟物业公司单独结清，否则无法退款。</span>
                    </div>
                </div>
            </div>

            <!-- 未完成手续 -->
            <div class="info-card">
                <div class="card-header">
                    <img src="../assets/images/procedure-icon.svg" alt="未完成手续图标" class="header-icon" />
                    <span>未完成手续</span>
                </div>

                <div class="procedure-list">
                    <div v-for="(item, index) in unfinishedProcedures" :key="index" class="procedure-item">
                        <span class="procedure-name">{{ item.name }}</span>
                        <span class="procedure-status">{{ item.status }}</span>
                    </div>
                </div>
            </div>

            <!-- 底部签字确认区域 -->
            <div class="bottom-action">
                <div class="status-text">手续未完全，补全后进入退款流程</div>
                <van-button type="primary" block round size="large" @click="confirmSignature">签字确认</van-button>
            </div>
        </template>

        <!-- 签字弹窗 -->
        <van-popup v-model:show="showSignature" position="bottom" round :style="{ height: '50%' }">
            <SignatureCanvas 
                :key="signatureKey"
                @confirm="handleSignConfirm"
                @cancel="showSignature = false"
             />
        </van-popup>
    </div>
</template>

<style scoped>
.exit-form-signature {
    padding-bottom: 120px;
    /* 为底部操作栏留出空间 */
    background-color: #f1f1f1;
    background-image: url('../assets/images/entrance-notice-bg.png');
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: top center;
}

.info-card {
    padding: 30px;
}

.contract-card {
    background: linear-gradient(to bottom, #009AFF, #005FFF);
    color: white;
    margin: 30px;
    border-radius: 20px;
}

.info-item {
    font-size: 24px;
    line-height: 36px;
    margin-bottom: 12px;
}

.house-list {
    padding: 0 30px;
    margin-top: 60px;
}

.house-item {
    background-color: white;
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.house-header {
    display: flex;
    align-items: center;
    gap: 10px;
}

.room-icon {
    width: 30px;
    height: 30px;
}

.house-location {
    font-size: 30px;
    font-weight: 500;
    color: #242433;
}

.house-date {
    font-size: 24px;
    color: #919199;
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 30px;
    font-weight: 500;
    color: #242433;
    padding: 0 10px;
}

.card-header span {
    margin-left: 8px;
}

.settlement-list {
    background-color: #fff;
    border-radius: 20px;
    padding: 0 30px 30px;
}

.settlement-item {
    display: flex;
    align-items: center;
    padding: 30px 0;
    border-bottom: 1px solid #EEEEEE;
}

.item-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 12px;
}

.refund {
    background-color: white;
    color: #F24F4F;
    border: 1px solid #F24F4F;
}

.collect {
    color: #3583FF;
    border: 1px solid #1677FF;
}

.item-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
}

.item-name {
    font-size: 28px;
    color: #333;
}

.item-value {
    font-size: 24px;
    color: #5A616E;
    font-weight: 600;
}

.summary-box {
    background: linear-gradient(to bottom, #EFF6FF, #FFFFFF);
    padding: 16px;
    border-radius: 10px;
    margin-top: 20px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

.summary-label {
    font-size: 28px;
    color: #333;
}

.summary-value {
    font-size: 28px;
    color: #5A616E;
    font-weight: 600;
}

.refund-amount {
    color: #FF6900;
}

.utility-info {
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 20px;
    padding: 0 30px 30px;
}

.utility-item {
    display: flex;
    justify-content: space-between;
    padding: 30px 0;
    border-bottom: 1px solid #EEEEEE;
}

.utility-name {
    font-size: 28px;
    color: #333;
}

.utility-value {
    font-size: 24px;
    color: #5A616E;
    font-weight: 600;
}

.utility-warning {
    display: flex;
    align-items: center;
    color: #E9691F;
    font-size: 26px;
    margin-top: 30px;
}

.utility-warning span {
    margin-left: 8px;
}

.procedure-list {
    margin-top: 12px;
    background-color: #fff;
    border-radius: 20px;
    padding: 0 30px;
}

.procedure-item {
    display: flex;
    justify-content: space-between;
    padding: 30px 0;
    border-bottom: 1px solid #EEEEEE;
}

.procedure-name {
    font-size: 28px;
    color: #333;
}

.procedure-status {
    font-size: 24px;
    color: #FF0000;
}

.bottom-action {
    height: 120px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    padding: 0 30px;
    box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, 0.04);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.status-text {
    font-size: 26px;
    font-weight: 500;
    color: #242433;
    flex: 1;
}

:deep(.van-button--primary) {
    background-color: #3583FF;
    border-color: #3583FF;
    height: 90px;
    font-size: 34px;
    width: 240px;
}

.header-icon {
    width: 30px;
    height: 30px;
}

/* 自定义 van-nav-bar 样式 */
:deep(.van-nav-bar .van-icon) {
    color: #333333;
}
</style>