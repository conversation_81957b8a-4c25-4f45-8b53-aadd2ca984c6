<script setup lang="ts">
import { ref } from 'vue'

// 定义需要缓存的组件名称列表
const includeComponents = ref([
	'Home',
	'BookingList',
	'BookingCreate',
	'EntryManagement',
	'ExitManagement',
	'RoomStateDiagram'
])
</script>

<template>
	<!-- 使用 keep-alive 缓存指定的组件 -->
	<keep-alive :include="includeComponents">
		<router-view />
	</keep-alive>
</template>

<style scoped>
/* 清除全局外边距和内边距 */
:root {
	margin: 0;
	padding: 0;
}

.logo {
	height: 6em;
	padding: 1.5em;
	will-change: filter;
	transition: filter 300ms;
}

.logo:hover {
	filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.vue:hover {
	filter: drop-shadow(0 0 2em #42b883aa);
}

:deep(.van-nav-bar--fixed) {
	z-index: 1000 !important;
}
</style>
