# 定单详情页面功能说明

## 页面概述
`BookingDetail.vue` 是定单详情页面，用于显示定单的详细信息，包括定单信息和签约信息两个主要区块。

## 功能特性

### 1. 页面结构
- **顶部导航栏**: 显示页面标题"已转签"，支持返回上一页
- **定单信息区块**: 显示定单相关的基本信息
- **签约信息区块**: 显示签约相关的详细信息

### 2. 信息展示

#### 定单信息
- 客户姓名
- 意向房源
- 应收定金
- 已收定金
- 缴纳日期
- 创建日期
- 创建人

#### 签约信息
- 签约客户姓名
- 签约房源
- 合同周期
- 签约日期
- 创建人

### 3. 设计规范
- **区块标题**: 蓝色渐变背景，带有蓝色指示条
- **信息展示**: 标签-值的形式，清晰易读
- **响应式布局**: 适配不同屏幕尺寸
- **圆角卡片**: 现代化的卡片设计

### 4. 路由支持
- 路径: `/booking-detail/:id?`
- 支持通过路由参数传递定单ID
- 从定单列表页面点击可跳转到此页面

## 使用方式

### 从代码中调用
```javascript
// 跳转到详情页面
router.push({ name: 'BookingDetail', params: { id: orderId } })
```

### 从列表页面
在定单列表页面中，点击任意定单项即可跳转到对应的详情页面。

## 技术实现
- 使用Vue 3 Composition API
- TypeScript类型支持
- Vant UI组件库
- 响应式设计
- 路由参数处理

## 数据结构

### 定单详情数据
```typescript
interface OrderDetail {
  customerName: string        // 客户姓名
  intendedProperty: string   // 意向房源
  expectedDeposit: string    // 应收定金
  receivedDeposit: string    // 已收定金
  paymentDate: string        // 缴纳日期
  createDate: string         // 创建日期
  creator: string            // 创建人
}
```

### 签约信息数据
```typescript
interface ContractDetail {
  customerName: string       // 签约客户姓名
  property: string          // 签约房源
  contractPeriod: string    // 合同周期
  signDate: string          // 签约日期
  creator: string           // 创建人
}
```

## 扩展功能
- 支持根据路由参数动态加载详情数据
- 可扩展支持编辑功能
- 可添加更多操作按钮
- 支持数据实时更新 