# 进场管理调整和通知功能完善说明

## 功能概述

根据参考项目 `/Users/<USER>/workspace/wyzgpt/wyzgpt-admin-h5/src/views/operationManage/entryManage.vue`，完善了 `EntryManagement.vue` 中的"调整"和"通知客户"功能。

## 主要修改内容

### 1. API 接口导入

**文件**: `src/views/EntryManagement.vue`

```javascript
// 新增导入
import { getEntryList, initEntry, getUnenteredRooms, getEnterDetail, notifyCustomer, type EnterQueryDTO, type EnterVo } from '../api/entry'
```

**新增接口**:
- `getEnterDetail`: 获取进场单详情（用于调整功能）
- `notifyCustomer`: 通知客户（发送进场通知单）

### 2. 调整功能实现

**新增方法**: `handleAdjust`

```javascript
// 调整进场信息
const handleAdjust = async (item: EnterVo) => {
    try {
        // 获取进场单详情
        const res = await getEnterDetail(item.id)
        
        if (res.code === 200 && res.data) {
            showToast('加载详情成功')
            
            // 跳转到进场办理页面，传递详情数据和编辑模式
            router.push({
                name: 'EntryProcess',
                query: {
                    entryId: item.id,
                    contractId: item.contractId,
                    contractNo: item.contractNo,
                    tenantName: item.tenantName,
                    mode: 'edit' // 标识为编辑模式
                },
                state: {
                    entryData: res.data // 通过路由状态传递详情数据
                }
            })
        }
    } catch (error) {
        console.error('获取进场详情失败:', error)
        showToast('获取进场详情失败')
    }
}
```

**功能特点**:
- 获取已办理进场单的详情数据
- 跳转到进场办理页面进行编辑
- 通过路由状态传递数据避免重复API调用
- 支持编辑模式标识

### 3. 通知客户功能实现

**新增方法**: `handleNotifyCustomer`

```javascript
// 通知客户
const handleNotifyCustomer = async (item: EnterVo) => {
    try {
        await showConfirmDialog({
            title: '确认通知客户',
            message: `确定要给承租方"${item.tenantName}"发送进场通知单吗？`,
        })
        
        const res = await notifyCustomer(item.id)
        
        if (res.code === 200) {
            showToast('通知发送成功')
        } else {
            showToast(res.msg || '通知发送失败')
        }
    } catch (error) {
        console.error('通知客户失败:', error)
        showToast('通知客户失败')
    }
}
```

**功能特点**:
- 用户确认对话框防止误操作
- 调用后端API发送进场通知单
- 完善的错误处理和用户提示

### 4. UI 界面优化

#### 按钮布局调整

**修改**: 为已办理状态的进场单添加操作按钮

```html
<!-- 待办理状态：只显示"办理进场"按钮 -->
<van-button type="primary" size="small" round @click.stop="handleEntryProcess(item)"
    class="action-btn" v-if="item.status === 0">
    办理进场
</van-button>

<!-- 已办理状态：显示"调整"和"通知客户"按钮 -->
<van-button type="primary" size="small" round @click.stop="handleAdjust(item)"
    class="action-btn" v-if="item.status === 1">
    调整
</van-button>
<van-button type="warning" size="small" round @click.stop="handleNotifyCustomer(item)"
    class="action-btn notify-btn" v-if="item.status === 1">
    通知客户
</van-button>
```

#### 样式优化

```less
.info-handle {
    display: flex;
    justify-content: flex-end;
    gap: 12px;           // 新增：按钮间距
    flex-wrap: wrap;     // 新增：支持换行
}

.notify-btn {
    background: #ff9500;
    border-color: #ff9500;
    color: #fff;
}
```

### 5. EntryProcess 页面编辑模式支持

#### 页面模式管理

**新增字段**:
```javascript
const pageMode = ref('create') // 页面模式: create-新建, edit-编辑
```

#### 动态标题

```html
<van-nav-bar :title="pageMode === 'edit' ? '调整进场' : '进场办理'" left-arrow fixed placeholder @click-left="onClickLeft" />
```

#### 数据结构适配

**优化**: `setEntryDetailData` 函数支持不同数据结构

```javascript
// 根据不同模式适配数据结构
if (pageMode.value === 'edit') {
    // 编辑模式：数据来自getEnterDetail接口
    const enterData = data.enter || data
    const contract = data.contract || {}
    // ... 编辑模式数据处理
} else {
    // 新建模式：数据来自initEntry接口
    const contract = data.contract || data
    // ... 新建模式数据处理
}
```

#### 页面初始化逻辑

```javascript
onMounted(() => {
    // 获取页面模式参数
    if (route.query.mode) {
        pageMode.value = route.query.mode as string
    }
    
    // 优先使用路由状态中传递的详情数据
    const entryData = history.state?.entryData
    if (entryData) {
        setEntryDetailData(entryData)
    } else if (entryId.value && pageMode.value === 'edit') {
        // 编辑模式且没有传递数据时，调用详情接口
        fetchEntryDetail()
    }
    // ... 其他初始化逻辑
})
```

## 功能流程图

```
进场管理列表
    ├── 待办理状态 (status = 0)
    │   └── [办理进场] → 房源选择 → 进场办理页面(新建模式)
    │
    └── 已办理状态 (status = 1)
        ├── [调整] → 获取详情 → 进场办理页面(编辑模式)
        └── [通知客户] → 确认对话框 → 发送通知API → 成功提示
```

## 技术要点

### 1. 路由状态传递
- 使用 `router.push({ state: { entryData: data } })` 传递数据
- 通过 `history.state?.entryData` 获取传递的数据
- 避免重复API调用，提升性能

### 2. 数据结构适配
- 新建模式：使用 `initEntry` 接口返回的数据结构
- 编辑模式：使用 `getEnterDetail` 接口返回的数据结构
- 统一的 `setEntryDetailData` 函数处理不同数据源

### 3. 用户体验优化
- 确认对话框防止误操作
- 详细的成功/失败提示
- 清晰的按钮颜色区分（调整-蓝色，通知-橙色）
- 响应式布局支持多按钮显示

### 4. 错误处理
- 完善的 try-catch 错误捕获
- 友好的错误提示信息
- 接口返回状态码检查

## 测试建议

1. **调整功能测试**:
   - 选择已办理状态的进场单
   - 点击"调整"按钮
   - 验证是否正确跳转到编辑模式的进场办理页面
   - 验证数据是否正确加载和显示

2. **通知客户功能测试**:
   - 选择已办理状态的进场单
   - 点击"通知客户"按钮
   - 验证确认对话框是否正常显示
   - 确认后验证API调用和成功提示

3. **界面测试**:
   - 验证不同状态下按钮显示是否正确
   - 验证按钮样式和布局是否美观
   - 验证页面标题是否根据模式正确显示

## 兼容性说明

- 保持向前兼容，不影响原有的"办理进场"功能
- 新增功能完全独立，可独立测试和调试
- 遵循现有的代码风格和架构模式

## 完成状态

✅ 调整功能实现  
✅ 通知客户功能实现  
✅ UI界面优化  
✅ 编辑模式支持  
✅ 错误处理完善  
✅ 代码构建测试通过  

所有功能已完成开发并通过构建测试，可以进行功能测试和部署。 