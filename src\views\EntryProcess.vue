<script lang="ts">
export default {
  name: 'EntryProcess'
}
</script>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import type { PickerOption } from 'vant'
import { saveEntry, getEnterDetail, type EnterAddDTO, type EnterRoomAddDTO, type EnterRoomAssetsAddDTO } from '../api/entry'
import { getToken } from '../utils/auth'

const router = useRouter()
const route = useRoute()

// 页面状态
const loading = ref(false)
const expandedRooms = ref<string[]>([])

// 返回上一页
const onClickLeft = () => {
    router.back()
}

// 基本信息
const basicInfo = ref({
    contractNo: '',
    contractPurpose: '',
    rentPeriod: '',
    tenantName: ''
})

// 房源数据
const roomList = ref<EnterRoomAddDTO[]>([])

// 资产选择相关
const showAssetPicker = ref(false)
const selectedAssets = ref<string[]>([])
const allAssets = ref<any[]>([])
const currentRoomIndex = ref(-1)

// 给承租方发送通知
const sendNotice = ref(true)

// 页面参数
const entryId = ref('')
const contractId = ref('')
const contractUnionId = ref('')
const projectId = ref('')
const pageMode = ref('create') // 页面模式: create-新建, edit-编辑, view-查看

// 日期选择器相关
const showDatePickerPopup = ref(false)
const currentDateRoomIndex = ref(-1)
const tempDate = ref<string[]>(['2024', '01', '01'])

// 是否为查看模式
const isViewMode = computed(() => pageMode.value === 'view')

// 设置进场单详情数据
const setEntryDetailData = (data: any) => {
    console.log('原始数据:', data)
    console.log('页面模式:', pageMode.value)

    // 根据不同模式适配数据结构
    if (pageMode.value === 'edit') {
        // 编辑模式：数据来自getEnterDetail接口
        const enterData = data.enter || data
        const contract = data.contract || {}

        basicInfo.value = {
            contractNo: contract.contractNo || enterData.contractNo || '',
            contractPurpose: getDictLabel('diversification_purpose', (contract.contractPurpose || enterData.contractPurpose)?.toString()) || '',
            rentPeriod: contract.startDate && contract.endDate
                ? `${contract.startDate} 至 ${contract.endDate}`
                : enterData.rentStartDate && enterData.rentEndDate
                    ? `${enterData.rentStartDate} 至 ${enterData.rentEndDate}`
                    : '',
            tenantName: contract.customerName || enterData.tenantName || ''
        }

        // 设置房源列表 - 编辑模式下从enterRoomList获取
        roomList.value = data.enterRoomList || []

        // 设置进场单ID（用于保存时的更新操作）
        if (enterData.id) {
            entryId.value = enterData.id
        }
    } else {
        // 新建模式：数据来自initEntry接口
        const contract = data.contract || data
        basicInfo.value = {
            contractNo: contract.contractNo || '',
            contractPurpose: getDictLabel('diversification_purpose', contract.contractPurpose?.toString()) || '',
            rentPeriod: contract.startDate && contract.endDate
                ? `${contract.startDate} 至 ${contract.endDate}`
                : '',
            tenantName: contract.customerName || ''
        }

        // 设置房源列表 - 新建模式下从enterRoomList获取
        roomList.value = data.enterRoomList || data.roomList || []
    }

    // 确保每个房间都有必要的字段
    roomList.value = roomList.value.map(room => ({
        ...room,
        assetList: room.assetList || []
    }))

    console.log('处理后的基本信息:', basicInfo.value)
    console.log('处理后的房源列表:', roomList.value)

    // 默认展开第一个房间
    if (roomList.value.length > 0) {
        expandedRooms.value = [roomList.value[0].roomId]
    }
}

// 获取进场单详情（备用方法，当没有传递entryData时使用）
const fetchEntryDetail = async () => {
    if (!entryId.value) return

    try {
        loading.value = true
        const response = await getEnterDetail(entryId.value)
        if (response.code === 200 && response.data) {
            setEntryDetailData(response.data)
        }
    } catch (error) {
        console.error('获取进场单详情失败:', error)
        showToast('获取进场单详情失败')
    } finally {
        loading.value = false
    }
}

// 获取字典标签文本
const getDictLabel = (dictType: string, value: string): string => {
    // 这里可以实现字典转换逻辑
    const dictMap: Record<string, Record<string, string>> = {
        'diversification_purpose': {
            '1': '办公',
            '2': '商业',
            '3': '仓储',
            '4': '其他'
        }
    }
    return dictMap[dictType]?.[value] || value
}

// 切换房间展开状态
const toggleRoomExpand = (roomId: string) => {
    const index = expandedRooms.value.indexOf(roomId)
    if (index > -1) {
        expandedRooms.value.splice(index, 1)
    } else {
        expandedRooms.value.push(roomId)
    }
}

// 显示资产选择器
const showAssetSelector = (roomIndex: number) => {
    currentRoomIndex.value = roomIndex
    showAssetPicker.value = true
    fetchAllAssets()
}

// 获取所有资产列表
const fetchAllAssets = async () => {
    try {
        // 模拟资产数据 - 实际项目中应该调用真实的API
        allAssets.value = [
            { id: '1', name: '床', specification: '1.5米*1米', category: 1 },
            { id: '2', name: '空调', specification: '2P', category: 2 },
            { id: '3', name: '空调', specification: '2P', category: 2 },
            { id: '4', name: '电表', specification: '', category: 3 },
            { id: '5', name: '冷水表', specification: '', category: 3 },
            { id: '6', name: '热水表', specification: '', category: 3 }
        ]
    } catch (error) {
        console.error('获取资产列表失败:', error)
        showToast('获取资产列表失败')
    }
}

// 检查资产是否已经添加
const isAssetAlreadyAdded = (assetId: string) => {
    const currentRoom = roomList.value[currentRoomIndex.value]
    return currentRoom?.assetList?.some(asset => asset.id === assetId) || false
}

// 添加选中的资产
const handleAddSelectedAssets = () => {
    if (currentRoomIndex.value === -1) {
        showToast('请先选择房间')
        return
    }

    const currentRoom = roomList.value[currentRoomIndex.value]
    if (!currentRoom.assetList) {
        currentRoom.assetList = []
    }

    const newAssets = selectedAssets.value
        .filter(id => !isAssetAlreadyAdded(id))
        .map(id => {
            const asset = allAssets.value.find(a => a.id === id)
            if (asset) {
                return {
                    category: asset.category,
                    name: asset.name,
                    specification: asset.specification || '',
                    count: 1,
                    isMissing: false,
                    isAdd: true
                }
            }
            return null
        })
        .filter(Boolean) as EnterRoomAssetsAddDTO[]

    if (newAssets.length === 0) {
        showToast('所选资产已存在')
        return
    }

    currentRoom.assetList.push(...newAssets)
    showAssetPicker.value = false
    selectedAssets.value = []
    showToast(`成功添加${newAssets.length}个配套`)
}

// 删除资产
const removeAsset = (roomIndex: number, assetIndex: number) => {
    const room = roomList.value[roomIndex]
    if (room?.assetList) {
        room.assetList.splice(assetIndex, 1)
        showToast('删除成功')
    }
}



// 更新水电度数
const updateUtilityReading = (room: EnterRoomAddDTO, field: string, value: string | number) => {
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/,/g, '')) : value
        ; (room as any)[field] = isNaN(numValue) ? 0 : numValue
}

// 验证表单数据
const validateForm = (): boolean => {
    // 检查是否有房源
    if (roomList.value.length === 0) {
        showToast('请至少选择一个房源')
        return false
    }

    // 检查每个房源的进场日期
    for (const room of roomList.value) {
        if (!room.enterDate) {
            showToast(`请设置房源"${room.roomName}"的进场日期`)
            return false
        }
    }

    return true
}

// 保存进场办理
const handleSave = async () => {
    if (loading.value) return

    if (!validateForm()) {
        return
    }

    try {
        await showConfirmDialog({
            title: '确认保存',
            message: '确定要保存进场办理信息吗？',
        })

        loading.value = true

        // 构造保存数据 - 统一使用简洁的数据结构
        const saveData: EnterAddDTO = {
            id: entryId.value || undefined,
            projectId: projectId.value || undefined,
            contractId: contractId.value,
            contractUnionId: contractUnionId.value || undefined,
            isNotify: sendNotice.value,
            roomList: roomList.value.map(room => ({
                roomId: room.roomId,
                roomName: room.roomName,
                propertyType: room.propertyType,
                parcelName: room.parcelName,
                buildingName: room.buildingName,
                enterDate: room.enterDate,
                remark: room.remark,
                coldWaterReading: room.coldWaterReading,
                hotWaterReading: room.hotWaterReading,
                elecMeterReading: room.elecMeterReading,
                assetList: room.assetList?.map(asset => {
                    const assetData: any = {
                        category: asset.category,
                        name: asset.name,
                        specification: asset.specification,
                        count: asset.count,
                        isMissing: asset.isMissing,
                        isAdd: asset.isAdd,
                        isDel: asset.isDel || false
                    }

                    // 对于编辑模式，只为已存在的资产传递enterRoomId
                    // 新添加的资产不传递任何ID字段
                    if (asset.enterRoomId && !asset.isAdd) {
                        assetData.enterRoomId = asset.enterRoomId
                    }

                    return assetData
                }) || []
            }))
        }

        console.log('保存数据:', saveData)
        console.log('页面参数:', {
            entryId: entryId.value,
            projectId: projectId.value,
            contractId: contractId.value,
            contractUnionId: contractUnionId.value,
            pageMode: pageMode.value
        })

        const response = await saveEntry(saveData)
        if (response.code === 200) {
            showToast('保存成功')
            router.back()
        } else {
            showToast(response.msg || '保存失败')
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('保存失败:', error)
            showToast('保存失败')
        }
    } finally {
        loading.value = false
    }
}

// 在页面挂载时，从路由参数中获取数据
onMounted(() => {
    // 获取基本参数
    if (route.query.contractId) {
        contractId.value = route.query.contractId as string
    }
    if (route.query.entryId) {
        entryId.value = route.query.entryId as string
    }
    if (route.query.contractUnionId) {
        contractUnionId.value = route.query.contractUnionId as string
    }
    if (route.query.projectId) {
        projectId.value = route.query.projectId as string
    }
    if (route.query.mode) {
        pageMode.value = route.query.mode as string
    }

    // 优先使用路由状态中传递的详情数据
    const entryData = history.state?.entryData
    if (entryData) {
        console.log('使用传递的详情数据:', entryData)
        setEntryDetailData(entryData)
    } else if (entryId.value && pageMode.value === 'edit') {
        // 编辑模式且没有传递数据时，调用详情接口
        console.log('编辑模式，调用详情接口获取数据')
        fetchEntryDetail()
    } else {
        // 没有传递详情数据时，构造基本信息
        console.log('未找到initEntry数据，使用查询参数构造基本信息')
        if (route.query.contractNo) {
            basicInfo.value.contractNo = route.query.contractNo as string
        }
        if (route.query.tenantName) {
            basicInfo.value.tenantName = route.query.tenantName as string
        }

        // 如果有roomIds参数，构造基本的房源列表
        if (route.query.roomIds) {
            const roomIds = (route.query.roomIds as string).split(',')
            roomList.value = roomIds.map((roomId, index) => ({
                id: undefined,
                enterId: undefined,
                roomId: roomId,
                roomName: `房间${index + 1}`, // 临时名称
                propertyType: undefined,
                parcelName: '',
                buildingName: '',
                enterDate: '',
                elecMeterReading: undefined,
                coldWaterReading: undefined,
                hotWaterReading: undefined,
                remark: '',
                assetList: [],
                isDel: false
            }))

            // 默认展开第一个房间
            if (roomList.value.length > 0) {
                expandedRooms.value = [roomList.value[0].roomId]
            }
        }
    }
})

// 格式化日期时间
const formatDate = (dateStr: string): string => {
    if (!dateStr) return ''
    return dateStr.split('T')[0]
}

// 获取资产状态文本
const getAssetStatusText = (isMissing: boolean): string => {
    return isMissing ? '缺失' : '完好'
}

// 获取资产状态类名
const getAssetStatusClass = (isMissing: boolean): string => {
    return isMissing ? 'missing' : 'good'
}

// 显示日期选择器
const showDatePicker = (roomIndex: number) => {
    currentDateRoomIndex.value = roomIndex
    const room = roomList.value[roomIndex]
    if (room.enterDate) {
        const date = new Date(room.enterDate)
        tempDate.value = [
            date.getFullYear().toString(),
            (date.getMonth() + 1).toString().padStart(2, '0'),
            date.getDate().toString().padStart(2, '0')
        ]
    } else {
        const today = new Date()
        tempDate.value = [
            today.getFullYear().toString(),
            (today.getMonth() + 1).toString().padStart(2, '0'),
            today.getDate().toString().padStart(2, '0')
        ]
    }
    showDatePickerPopup.value = true
}

// 确认日期选择
const onDateConfirm = () => {
    if (currentDateRoomIndex.value >= 0) {
        const room = roomList.value[currentDateRoomIndex.value]
        room.enterDate = tempDate.value.join('-')
    }
    showDatePickerPopup.value = false
}

// 取消日期选择
const onDateCancel = () => {
    showDatePickerPopup.value = false
}
</script>

<template>
    <div class="entry-process">
        <!-- 导航栏 -->
        <van-nav-bar :title="pageMode === 'edit' ? '调整进场' : pageMode === 'view' ? '进场详情' : '进场办理'" left-arrow fixed
            placeholder @click-left="onClickLeft" />

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 加载状态 -->
            <van-loading v-if="loading" class="loading-center" size="24px" vertical>
                加载中...
            </van-loading>

            <div v-else>
                <!-- 基本信息 -->
                <div class="basic-info-section">
                    <div class="basic-info-card">
                        <div class="info-content">
                            <div class="info-row">
                                <span class="info-label">合同编号：</span>
                                <span class="info-value">{{ basicInfo.contractNo }}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">承租方：</span>
                                <span class="info-value">{{ basicInfo.tenantName }}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">租期：</span>
                                <span class="info-value">{{ basicInfo.rentPeriod }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 进场房源 -->
                <div class="rooms-section">
                    <div class="section-title">
                        <img src="../assets/images/entrance-notice-icon.png" alt="房源" class="title-icon" />
                        <div class="section-title-text">
                            进场房源 · <span class="room-count">{{ roomList.length }}间</span>
                        </div>
                    </div>

                    <!-- 房源列表 -->
                    <div class="room-list">
                        <div v-for="(room, roomIndex) in roomList" :key="room.roomId" class="room-card">
                            <!-- 房间头部 -->
                            <div class="room-header" @click="toggleRoomExpand(room.roomId)">
                                <div class="room-info">
                                    <div class="room-name-row">
                                        <img class="house-icon" src="../assets/images/house-icon.svg" alt="房源" />
                                        <span class="room-name">{{ room.roomName }}</span>
                                    </div>
                                    <div class="room-date">进场日期：{{ formatDate(room.enterDate || '') }}</div>
                                </div>
                                <van-icon :name="expandedRooms.includes(room.roomId) ? 'arrow-up' : 'arrow-down'"
                                    class="expand-icon" />
                            </div>

                            <!-- 房间详情 -->
                            <div v-if="expandedRooms.includes(room.roomId)" class="room-content">
                                <!-- 进场日期 -->
                                <div class="form-section">
                                    <van-field label="进场日期" readonly :is-link="!isViewMode" input-align="right"
                                        label-width="140px" :model-value="formatDate(room.enterDate || '')"
                                        placeholder="请选择进场日期" @click="!isViewMode && showDatePicker(roomIndex)" />
                                </div>

                                <!-- 房间配套情况 -->
                                <div class="assets-section">
                                    <div class="section-title-blue">
                                        <div class="section-title-blue-text">
                                            <span class="tag"></span>
                                            房间配套情况
                                        </div>
                                        <van-button v-if="!isViewMode" type="primary" size="mini"
                                            @click="showAssetSelector(roomIndex)" class="add-btn">
                                            + 添加
                                        </van-button>
                                    </div>

                                    <div v-if="!room.assetList || room.assetList.length === 0" class="empty-assets">
                                        暂无配套设施
                                    </div>

                                    <div v-for="(asset, assetIndex) in room.assetList" :key="assetIndex"
                                        class="asset-item">
                                        <div class="asset-info">
                                            <div class="asset-label-container">
                                                <van-icon v-if="asset.isAdd && !isViewMode" name="delete-o"
                                                    class="delete-icon" @click="removeAsset(roomIndex, assetIndex)" />
                                                <span class="asset-name">{{ asset.name }}{{ asset.specification ?
                                                    `(${asset.specification})` : '' }}</span>
                                                                                                <span class="asset-count">x{{ asset.count || 1 }}</span>

                                            </div>
                                            <div class="asset-status" v-if="!asset.isAdd">
                                                <div class="switch-container">
                                                    <span class="switch-label">{{ !asset.isMissing ? '齐全' : '缺失'
                                                        }}</span>
                                                    <van-switch v-model="asset.isMissing" :disabled="isViewMode"
                                                        size="20" active-color="#ff4444" inactive-color="#52c41a"
                                                        @change="() => { }" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 水电度数（只读记录） -->
                                <div class="utility-section">
                                    <div class="section-title-blue">
                                        <div class="section-title-blue-text">
                                            <span class="tag"></span>
                                            水电读数（只做记录）
                                        </div>
                                    </div>

                                    <van-field label="电表" placeholder="请输入度数" input-align="right" label-width="140px"
                                        type="number" :readonly="isViewMode" v-model="room.elecMeterReading"
                                        @update:model-value="!isViewMode && ((value: string | number) => updateUtilityReading(room, 'elecMeterReading', value))">
                                        <template #button>
                                            <span class="unit">度</span>
                                        </template>
                                    </van-field>

                                    <van-field label="冷水表" placeholder="请输入度数" input-align="right" label-width="140px"
                                        type="number" :readonly="isViewMode" v-model="room.coldWaterReading"
                                        @update:model-value="!isViewMode && ((value: string | number) => updateUtilityReading(room, 'coldWaterReading', value))">
                                        <template #button>
                                            <span class="unit">吨</span>
                                        </template>
                                    </van-field>

                                    <van-field label="热水表" placeholder="请输入度数" input-align="right" label-width="140px"
                                        type="number" :readonly="isViewMode" v-model="room.hotWaterReading"
                                        @update:model-value="!isViewMode && ((value: string | number) => updateUtilityReading(room, 'hotWaterReading', value))">
                                        <template #button>
                                            <span class="unit">吨</span>
                                        </template>
                                    </van-field>
                                </div>

                                <!-- 备注 -->
                                <div class="remark-section">
                                    <div class="section-title-blue">
                                        <div class="section-title-blue-text">
                                            <span class="tag"></span>
                                            备注
                                        </div>
                                    </div>
                                    <van-field v-model="room.remark" rows="3" autosize type="textarea"
                                        :readonly="isViewMode" placeholder="请输入内容" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 给承租方发送进场通知单 -->
                <div v-if="!isViewMode" class="notice-section">
                    <van-checkbox v-model="sendNotice">给承租方发送进场通知单</van-checkbox>
                </div>
            </div>
        </div>

        <!-- 底部操作区域 -->
        <div v-if="!isViewMode" class="bottom-action-bar">
            <div class="action-buttons">
                <van-button class="cancel-btn" @click="onClickLeft">取消</van-button>
                <van-button type="primary" class="submit-btn" :loading="loading" @click="handleSave">
                    提交
                </van-button>
            </div>
        </div>

        <!-- 资产选择弹窗 -->
        <van-popup v-model:show="showAssetPicker" position="bottom" round closeable close-icon-position="top-right"
            :style="{ height: '70%' }">
            <div class="asset-picker-popup">
                <div class="popup-header">选择配套</div>
                <div class="popup-content">
                    <van-checkbox-group v-model="selectedAssets">
                        <div class="asset-picker-list">
                            <div v-for="asset in allAssets" :key="asset.id" class="asset-picker-item">
                                <van-checkbox :name="asset.id" :disabled="isAssetAlreadyAdded(asset.id)">
                                    <div class="asset-info">
                                        <div class="asset-name">{{ asset.name }}</div>
                                        <div v-if="asset.specification" class="asset-spec">({{ asset.specification }})
                                        </div>
                                    </div>
                                </van-checkbox>
                            </div>
                        </div>
                    </van-checkbox-group>
                </div>
                <div class="popup-footer">
                    <van-button round block type="primary" color="#3583FF" @click="handleAddSelectedAssets"
                        :disabled="selectedAssets.length === 0">
                        确认 ({{ selectedAssets.length }})
                    </van-button>
                </div>
            </div>
        </van-popup>

        <!-- 日期选择器弹窗 -->
        <van-popup v-model:show="showDatePickerPopup" position="bottom">
            <van-date-picker v-model="tempDate" title="选择进场日期" @confirm="onDateConfirm" @cancel="onDateCancel" />
        </van-popup>
    </div>
</template>

<style lang="less" scoped>
.entry-process {
    min-height: 100vh;
    background-color: #F1F1F1;
    display: flex;
    flex-direction: column;
}

/* 内容区域 */
.content-area {
    flex: 1;
    margin-bottom: 140px;
}

/* 基本信息区域 */
.basic-info-section {
    height: 300px;
    box-sizing: border-box;
    padding: 30px;
    /* margin: 16px; */
    /* overflow: hidden; */
    background: url('../assets/images/entrance-notice-bg.png') no-repeat center center;
    background-size: cover;
}

.basic-info-card {
    background: linear-gradient(135deg, #3583FF 0%, #1677FF 100%);
    /* 蓝色渐变背景 */
    /* 预留背景图片位置
    background-image: url(''); 
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    */
    border-radius: 12px;
    padding: 30px 20px;
    color: #FFFFFF;
    /* 白色文字 */
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(53, 131, 255, 0.3);
}

.info-content {
    position: relative;
    z-index: 2;
}

.info-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 28px;
    line-height: 1.4;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    min-width: 120px;
}

.info-value {
    font-weight: 500;
    color: #FFFFFF;
    flex: 1;
}

/* 房源区域 */
.rooms-section {
    /* margin: 0 16px 16px; */
    box-sizing: border-box;
    padding: 20px 20px 0 20px;
    border-radius: 20px;
    position: relative;
    top: -20px;
    background-color: #f0f0f0;

    .section-title {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 32px;
        font-weight: 600;

        .title-icon {
            width: 40px;
            height: 40px;
        }

        .section-title-text {
            display: flex;
            align-items: center;
            margin-bottom: -10px;
        }

        .room-count {
            color: #3583FF;
        }
    }
}

.room-list {
    margin-top: 32px;
}

.room-card {
    background-color: #FFFFFF;
    border-radius: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

/* 房间头部 */
.room-header {
    display: flex;
    align-items: center;
    padding: 16px;
    cursor: pointer;
    border-bottom: 1px solid #F5F5F5;
}

.room-info {
    flex: 1;
}

.room-name-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.house-icon {
    width: 30px;
    height: 30px;
    margin-right: 16px;
}

.room-name {
    font-size: 28px;
    font-weight: 500;
    color: #242433;
}

.room-date {
    font-size: 24px;
    color: #919199;
}

.expand-icon {
    color: #C8C9CC;
    font-size: 16px;
}

/* 房间内容 */
.room-content {
    padding: 0 16px 16px;
}

.form-section {
    margin-bottom: 16px;
}

/* 蓝色标题样式 */
.section-title-blue {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 28px;
    font-weight: 500;
    // color: #1677FF;
    padding: 22px 0 22px 16px;
    border-bottom: 1px solid #E8EBFF;
    color: #000;

    .section-title-blue-text {
        display: flex;
        align-items: center;
        gap: 10px;
        .tag {
            width: 10px;
            height: 30px;
            background-color: #1677FF;
            border-radius: 8px;
            margin-right: 10px;
        }
    }
}

/* 添加按钮样式 */
.add-btn {
    height: 60px !important;
    font-size: 28px !important;
    font-weight: normal !important;
    padding: 0 12px !important;
}

/* 空状态样式 */
.empty-assets {
    text-align: center;
    color: #C8C9CC;
    font-size: 14px;
    padding: 20px 0;
}

/* 资产项目样式 */
.asset-item {
    padding: 16px 0;
    border-bottom: 1px solid #F5F5F5;
}

.asset-item:last-child {
    border-bottom: none;
}

.asset-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.asset-label-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.delete-icon {
    color: #FF4D4F;
    font-size: 30px;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.delete-icon:hover {
    background-color: #FFF2F0;
    color: #FF7875;
}

.asset-name {
    font-size: 28px;
    color: #242433;
    line-height: 1.4em;
}
.asset-count {
    font-size: 24px;
    color: #919199;
}

.asset-status {
    flex-shrink: 0;
}

.status-tag {
    font-size: 24px;
    font-weight: 500;
    border: none;
    min-width: 60px;
    text-align: center;
}

.status-tag.clickable {
    cursor: pointer;
    transition: all 0.3s ease;
}

.status-tag.clickable:hover {
    transform: scale(1.05);
}

.status-tag.clickable:active {
    transform: scale(0.95);
}

.missing-checkbox :deep(.van-checkbox__label) {
    font-size: 24px;
    color: #242433;
}

/* 水电度数区域 */
.utility-section {
    margin-bottom: 16px;
}

/* 备注区域 */
.remark-section {
    margin-bottom: 16px;
}

/* 通知区域 */
.notice-section {
    margin: -20px 20px 20px;
    padding: 20px;
    background-color: #FFFFFF;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.notice-section :deep(.van-checkbox__label) {
    font-size: 28px;
    color: #242433;
}

/* 底部操作栏 */
.bottom-action-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #FFFFFF;
    box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.04);
    z-index: 10;
}

.action-buttons {
    padding: 20px 32px;
    display: flex;
    gap: 20px;
}

.cancel-btn {
    flex: 1;
    height: 88px;
    line-height: 88px;
    font-size: 32px;
    border-radius: 45px;
    background-color: #F5F5F5;
    border-color: #F5F5F5;
    color: #666666;
}

.submit-btn {
    flex: 1;
    height: 88px;
    line-height: 88px;
    font-size: 32px;
    border-radius: 45px;
    background-color: #3583FF;
    border-color: #3583FF;
}

/* 加载状态 */
.loading-center {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    flex-direction: column;
}

/* 资产选择弹窗样式 */
.asset-picker-popup {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
}

.popup-header {
    height: 88px;
    line-height: 88px;
    text-align: center;
    font-size: 32px;
    font-weight: 600;
    color: #242433;
    border-bottom: 1px solid #E8EBFF;
    position: relative;
}

.popup-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.asset-picker-list {
    padding: 0 32px;
}

.asset-picker-item {
    padding: 24px 0;
    border-bottom: 1px solid #E8EBFF;
}

.asset-picker-item:last-child {
    border-bottom: none;
}

.asset-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.asset-name {
    font-size: 28px;
    color: #242433;
}

.asset-spec {
    font-size: 24px;
    color: #919199;
}

.popup-footer {
    padding: 20px 32px;
    border-top: 1px solid #E8EBFF;
    background-color: #FFFFFF;
}

:deep(.van-checkbox__label) {
    flex: 1;
}

:deep(.van-checkbox) {
    align-items: center;
    padding: 0 16px;
}

:deep(.van-checkbox__icon) {
    height: 36px;
    width: 36px;
    border-radius: 36px !important;
}

:deep(.van-checkbox__icon--checked) {
    background-color: #3583FF;
    border-color: #3583FF;
}

:deep(.van-button--primary) {
    height: 88px;
    line-height: 88px;
    font-size: 32px;
}

:deep(.van-popup) {
    max-height: 85vh;
}

:deep(.van-checkbox--disabled) {
    opacity: 0.6;
}

/* 单位文字样式 */
.unit {
    color: #919199;
    font-size: 28px;
}

/* 开关容器样式 */
.switch-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.switch-label {
    font-size: 26px;
    font-weight: 500;
    color: #242433;
    min-width: 60px;
}
</style>