/**
 * Keep-Alive 调试工具
 * 用于深入分析 keep-alive 不生效的原因
 */

export class KeepAliveDebug {
  private static componentStates = new Map<string, {
    mounted: number,
    activated: number,
    deactivated: number,
    unmounted: number,
    lastActivated: Date | null,
    lastDeactivated: Date | null
  }>()

  /**
   * 记录组件挂载
   */
  static recordMounted(componentName: string): void {
    const state = this.getOrCreateState(componentName)
    state.mounted++
    console.log(`🔵 [${componentName}] onMounted - 第 ${state.mounted} 次挂载`)
    this.printComponentState(componentName)
  }

  /**
   * 记录组件激活
   */
  static recordActivated(componentName: string): void {
    const state = this.getOrCreateState(componentName)
    state.activated++
    state.lastActivated = new Date()
    console.log(`🟢 [${componentName}] onActivated - 第 ${state.activated} 次激活`)
    this.printComponentState(componentName)
  }

  /**
   * 记录组件失活
   */
  static recordDeactivated(componentName: string): void {
    const state = this.getOrCreateState(componentName)
    state.deactivated++
    state.lastDeactivated = new Date()
    console.log(`🟡 [${componentName}] onDeactivated - 第 ${state.deactivated} 次失活`)
    this.printComponentState(componentName)
  }

  /**
   * 记录组件卸载
   */
  static recordUnmounted(componentName: string): void {
    const state = this.getOrCreateState(componentName)
    state.unmounted++
    console.log(`🔴 [${componentName}] onUnmounted - 第 ${state.unmounted} 次卸载`)
    this.printComponentState(componentName)
  }

  /**
   * 获取或创建组件状态
   */
  private static getOrCreateState(componentName: string) {
    if (!this.componentStates.has(componentName)) {
      this.componentStates.set(componentName, {
        mounted: 0,
        activated: 0,
        deactivated: 0,
        unmounted: 0,
        lastActivated: null,
        lastDeactivated: null
      })
    }
    return this.componentStates.get(componentName)!
  }

  /**
   * 打印组件状态
   */
  private static printComponentState(componentName: string): void {
    const state = this.getOrCreateState(componentName)
    console.log(`📊 [${componentName}] 状态统计:`, {
      mounted: state.mounted,
      activated: state.activated,
      deactivated: state.deactivated,
      unmounted: state.unmounted,
      isKeepAlive: state.mounted === 1 && state.unmounted === 0
    })
  }

  /**
   * 打印所有组件的详细报告
   */
  static printDetailedReport(): void {
    console.log('\n📋 Keep-Alive 详细报告:')
    console.log('=' .repeat(50))
    
    this.componentStates.forEach((state, componentName) => {
      const isKeepAlive = state.mounted === 1 && state.unmounted === 0
      const status = isKeepAlive ? '✅ 正常缓存' : '❌ 缓存失效'
      
      console.log(`\n🏷️  ${componentName} ${status}`)
      console.log(`   挂载次数: ${state.mounted}`)
      console.log(`   激活次数: ${state.activated}`)
      console.log(`   失活次数: ${state.deactivated}`)
      console.log(`   卸载次数: ${state.unmounted}`)
      
      if (state.lastActivated) {
        console.log(`   最后激活: ${state.lastActivated.toLocaleTimeString()}`)
      }
      if (state.lastDeactivated) {
        console.log(`   最后失活: ${state.lastDeactivated.toLocaleTimeString()}`)
      }
      
      // 诊断建议
      if (!isKeepAlive) {
        console.log('   🔍 可能的问题:')
        if (state.mounted > 1) {
          console.log('     - 组件被重复创建，keep-alive 未生效')
        }
        if (state.unmounted > 0) {
          console.log('     - 组件被卸载，可能不在 keep-alive 范围内')
        }
        if (state.activated === 0 && state.deactivated === 0) {
          console.log('     - 未触发 activated/deactivated，可能组件名称不匹配')
        }
      }
    })
    
    console.log('\n' + '='.repeat(50))
  }

  /**
   * 重置所有统计
   */
  static reset(): void {
    this.componentStates.clear()
    console.log('🔄 Keep-Alive 调试数据已重置')
  }
}

/**
 * 组合式 API 钩子
 */
export function useKeepAliveDebug(componentName: string) {
  return {
    recordMounted: () => KeepAliveDebug.recordMounted(componentName),
    recordActivated: () => KeepAliveDebug.recordActivated(componentName),
    recordDeactivated: () => KeepAliveDebug.recordDeactivated(componentName),
    recordUnmounted: () => KeepAliveDebug.recordUnmounted(componentName),
    printReport: () => KeepAliveDebug.printDetailedReport()
  }
}
