# 调整功能保存失败问题修复说明

## 问题描述

在进场管理的调整功能中，编辑模式下保存进场单时出现失败，通过对比成功和失败的API请求参数发现了数据结构的差异。

## 问题分析

### 成功的API请求参数（参考）
```json
{
    "id": "36abf1706acd8661d5fe0562a02389fd",
    "projectId": "108",
    "contractId": "bbcb9882861db9edabad6da5e0c4dbdb",
    "contractUnionId": "33e4fe0c57e0473782385bef7633d8b0",
    "isNotify": true,
    "roomList": [
        {
            "roomId": "19398",
            "roomName": "2B-01",
            "propertyType": 32,
            "parcelName": "/",
            "buildingName": "10幢",
            "enterDate": "2025-07-15",
            "remark": "",
            "assetList": [
                {
                    "enterRoomId": "c750f48a0fbd96ac80db714e44fe620b",
                    "category": 3,
                    "name": "电表",
                    "specification": "",
                    "count": 1,
                    "isMissing": false,
                    "isAdd": true,
                    "createByName": "",
                    "updateByName": "",
                    "isDel": false
                }
            ]
        }
    ]
}
```

### 失败的API请求参数（原代码生成）
```json
{
    "id": "36abf1706acd8661d5fe0562a02389fd",
    "projectId": "108",
    "contractId": "bbcb9882861db9edabad6da5e0c4dbdb",
    "contractUnionId": "33e4fe0c57e0473782385bef7633d8b0",
    "isNotify": true,
    "roomList": [
        {
            "id": "0e6ad23ca98d0e0753863b1c33371540",           // 多余字段
            "enterId": "36abf1706acd8661d5fe0562a02389fd",      // 多余字段
            "roomId": "19398",
            "roomName": "2B-01",
            "propertyType": 32,
            "parcelName": "/",
            "buildingName": "10幢",
            "enterDate": "2025-07-15",
            "elecMeterReading": 0,                              // 多余字段
            "coldWaterReading": 0,                              // 多余字段
            "hotWaterReading": 0,                               // 多余字段
            "remark": "",
            "assetList": [
                {
                    "id": "f330342f7d3d8e58e183a166db559135",   // 问题字段
                    "enterRoomId": "0e6ad23ca98d0e0753863b1c33371540",
                    "category": 3,
                    "name": "电表",
                    "specification": "",
                    "count": 1,
                    "isMissing": false,
                    "isAdd": true,
                    "isDel": false
                }
            ],
            "isDel": false                                      // 多余字段
        }
    ]
}
```

### 关键问题差异

1. **Room对象的复杂度**：
   - ✅ 成功：简洁的room对象，只包含必要字段
   - ❌ 失败：包含完整的编辑信息（id、enterId、水电度数、isDel等）

2. **Asset对象的ID处理**：
   - ✅ 成功：新添加的资产只有`enterRoomId`，没有`id`字段
   - ❌ 失败：新添加的资产既有`id`又有`enterRoomId`，可能造成冲突

3. **数据结构倾向**：
   - 后端API更期望类似新建模式的简洁数据结构
   - 编辑模式的完整字段信息反而导致保存失败

## 解决方案

### 修改策略

将编辑模式的保存数据结构调整为与成功案例一致的简洁格式：

1. **统一使用简洁的Room数据结构**
2. **Asset对象只在必要时传递ID信息**
3. **新添加的资产不传递任何ID字段**

### 具体修改

**文件位置**: `src/views/EntryProcess.vue`

**修改方法**: `handleSave` 中的数据构造逻辑

```javascript
// 构造保存数据 - 统一使用简洁的数据结构
const saveData: EnterAddDTO = {
    id: entryId.value || undefined,
    projectId: projectId.value || undefined,
    contractId: contractId.value,
    contractUnionId: contractUnionId.value || undefined,
    isNotify: sendNotice.value,
    roomList: roomList.value.map(room => ({
        roomId: room.roomId,
        roomName: room.roomName,
        propertyType: room.propertyType,
        parcelName: room.parcelName,
        buildingName: room.buildingName,
        enterDate: room.enterDate,
        remark: room.remark,
        assetList: room.assetList?.map(asset => {
            const assetData: any = {
                category: asset.category,
                name: asset.name,
                specification: asset.specification,
                count: asset.count,
                isMissing: asset.isMissing,
                isAdd: asset.isAdd,
                isDel: asset.isDel || false
            }
            
            // 对于编辑模式，只为已存在的资产传递enterRoomId
            // 新添加的资产不传递任何ID字段
            if (asset.enterRoomId && !asset.isAdd) {
                assetData.enterRoomId = asset.enterRoomId
            }
            
            return assetData
        }) || []
    }))
}
```

### 修改要点

1. **Room对象简化**：
   - 移除了 `id`、`enterId`、`isDel` 字段
   - 移除了水电度数字段（`elecMeterReading`、`coldWaterReading`、`hotWaterReading`）
   - 只保留核心的房源信息

2. **Asset对象优化**：
   - 新添加的资产（`isAdd: true`）不传递任何ID字段
   - 已存在的资产只在有`enterRoomId`时才传递
   - 移除了对`asset.id`的传递，避免ID冲突

3. **数据结构统一**：
   - 不再根据`pageMode`区分数据结构
   - 编辑模式和新建模式使用相同的简洁格式
   - 通过顶层的`id`字段区分新建和编辑操作

## 修复效果

### 修复后的数据结构预期
```json
{
    "id": "36abf1706acd8661d5fe0562a02389fd",
    "projectId": "108",
    "contractId": "bbcb9882861db9edabad6da5e0c4dbdb",
    "contractUnionId": "33e4fe0c57e0473782385bef7633d8b0",
    "isNotify": true,
    "roomList": [
        {
            "roomId": "19398",
            "roomName": "2B-01",
            "propertyType": 32,
            "parcelName": "/",
            "buildingName": "10幢",
            "enterDate": "2025-07-15",
            "remark": "",
            "assetList": [
                {
                    "category": 3,
                    "name": "电表",
                    "specification": "",
                    "count": 1,
                    "isMissing": false,
                    "isAdd": true,
                    "isDel": false
                    // 新添加的资产不传递enterRoomId或id
                }
            ]
        }
    ]
}
```

## 测试建议

1. **功能测试**：
   - 测试编辑模式下的保存功能是否正常
   - 验证新建模式的功能是否受影响
   - 测试添加新资产后的保存操作

2. **数据验证**：
   - 检查console日志中的保存数据结构
   - 确认与成功案例的数据格式一致
   - 验证后端API能正确处理简化后的数据

3. **兼容性测试**：
   - 确保不同场景下的数据都能正确保存
   - 验证编辑已有资产和添加新资产的混合场景

## 风险评估

### 低风险
- 数据结构简化，减少了复杂性
- 基于成功案例进行调整，可靠性高
- 保持了向前兼容性

### 注意事项
- 如果后端需要水电度数等信息，可能需要单独处理
- 已存在资产的更新逻辑需要验证是否正确

## 完成状态

✅ 问题分析完成  
✅ 数据结构差异识别  
✅ 代码修改实施  
✅ 构建测试通过  
🔄 等待功能测试验证  

修改已完成，建议进行实际测试验证保存功能是否正常工作。 