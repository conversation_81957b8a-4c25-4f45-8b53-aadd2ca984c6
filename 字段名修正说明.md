# 字段名修正说明

## 修改背景
根据定单接口文档（`.cursor/定单/定单-接口文档.md`），在`BookingAddDTO`数据模型中，房源名称字段定义为`roomName`，而不是之前代码中使用的`room`。

## 相关接口字段定义
```json
{
  "roomId": "string",     // 房源id
  "roomName": "string"    // 房源名称
}
```

## 修改内容

### 1. 表单数据字段修正
**文件**: `src/views/BookingCreate.vue`

**修改前**:
```javascript
const formData = reactive({
    // ...
    roomId: '',
    room: '',  // ❌ 错误字段名
    // ...
})
```

**修改后**:
```javascript
const formData = reactive({
    // ...
    roomId: '',
    roomName: '', // ✅ 正确字段名 - 根据接口文档使用roomName
    // ...
})
```

### 2. 模板字段绑定修正
**修改前**:
```vue
<van-field v-model="formData.room" required readonly name="room" label="意向房源">
```

**修改后**:
```vue
<van-field v-model="formData.roomName" required readonly name="roomName" label="意向房源">
```

### 3. 业务逻辑中的字段引用修正
涉及的修改位置：
- 项目变化时清空房源选择
- 业态类型变化时清空房源选择  
- 房源选择确认时赋值
- 表单验证逻辑

**修改前**:
```javascript
// 清空房源选择
formData.roomId = ''
formData.room = ''

// 房源选择确认
formData.room = value.selectedOptions[0].text

// 表单验证
if (!formData.room && !formData.isUncertainRoom) {
    showToast('请选择意向房源或勾选暂不确认房源')
    return false
}
```

**修改后**:
```javascript
// 清空房源选择
formData.roomId = ''
formData.roomName = ''

// 房源选择确认
formData.roomName = value.selectedOptions[0].text

// 表单验证
if (!formData.roomName && !formData.isUncertainRoom) {
    showToast('请选择意向房源或勾选暂不确认房源')
    return false
}
```

## 验证结果
- ✅ TypeScript类型检查通过
- ✅ 构建成功无错误
- ✅ 字段名与接口文档保持一致

## 影响范围
此修改确保了前端表单数据与后端API接口的字段名完全一致，避免了数据提交时的字段映射问题。

**相关文件**:
- `src/views/BookingCreate.vue` - 定单创建页面
- `src/api/booking.ts` - 定单相关API调用 