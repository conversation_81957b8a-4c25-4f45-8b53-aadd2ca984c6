# API 接口封装使用指南

## 概述

本项目基于 axios 封装了一套完整的 HTTP 请求库，参考了 `wyzgpt-admin-h5` 项目的设计，适配了移动端 H5 应用的需求。

## 特性

- ✅ TypeScript 完整类型支持
- ✅ 请求/响应拦截器
- ✅ 统一错误处理
- ✅ Token 自动注入
- ✅ 登录状态管理
- ✅ Vant UI 组件集成
- ✅ 环境配置支持
- ✅ 文件上传/下载支持

## 目录结构

```
src/api/
├── index.ts          # axios 封装主文件
├── user.ts           # 用户相关接口
├── crm.ts            # CRM 相关接口
├── booking.ts        # 定单相关接口
├── contract.ts       # 合同相关接口
├── collection.ts     # 催缴管理接口
├── entry.ts          # 进场管理接口
├── exit.ts           # 出场管理接口
├── room.ts           # 房源相关接口
├── organization.ts   # 组织架构接口
├── dict.ts           # 字典数据接口
├── home.ts           # 首页相关接口
└── README.md         # 使用说明
```

## 基本用法

### 1. 引入接口

```typescript
import { login, getUserInfo } from '@/api/user';
import { getDashboardStats } from '@/api/crm';
import { getCollectionDetail, payCollectionBill } from '@/api/collection';
```

### 2. 调用接口

```typescript
// GET 请求
const userInfo = await getUserInfo();

// POST 请求
const loginResult = await login({
  username: 'admin',
  password: '123456'
});

// 获取催缴详情
const collectionDetail = await getCollectionDetail('bill123');

// 催缴支付请求
const paymentResult = await payCollectionBill({
  billId: 'bill123',
  amount: 1000,
  paymentList: [
    {
      costId: 'cost123',
      payAmount: 1000
    }
  ]
});

// 带参数的 GET 请求
const customerList = await getCustomerList({
  page: 1,
  pageSize: 20,
  name: '客户名称'
});
```

### 3. 错误处理

```typescript
try {
  const result = await getUserInfo();
  console.log('用户信息:', result.data);
} catch (error) {
  console.error('请求失败:', error);
  // 错误已在拦截器中处理，这里可以做额外处理
}
```

## 接口定义规范

### 1. 接口参数类型定义

```typescript
export interface LoginData {
  username: string;
  password: string;
}
```

### 2. 响应数据类型定义

```typescript
export interface LoginRes {
  access_token: string;
  user_info: UserInfo;
}
```

### 3. 接口函数定义

```typescript
/**
 * 用户登录
 */
export function login(data: LoginData) {
  return http.post<LoginRes>('/auth/login', data);
}
```

## 配置说明

### 环境变量

在项目根目录创建环境变量文件：

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:3000/api

# .env.production
VITE_API_BASE_URL=https://api.example.com
```

### 应用配置

在 `src/config/index.ts` 中修改配置：

```typescript
export const APP_CONFIG = {
  api: {
    baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
    timeout: 12000,
  },
  // 其他配置...
};
```

## 拦截器功能

### 请求拦截器

- 自动添加 Authorization token
- 统一请求头处理

### 响应拦截器

- 统一错误处理
- Token 失效自动跳转登录
- 业务错误提示

## 常用方法

### HTTP 方法

```typescript
// GET 请求
http.get<T>(url, params?, config?)

// POST 请求
http.post<T>(url, data?, config?)

// PUT 请求
http.put<T>(url, data?, config?)

// DELETE 请求
http.delete<T>(url, params?, config?)
```

### 特殊方法

```typescript
// 文件上传
http.upload<T>(url, formData, config?)

// 文件下载
http.download(url, data?, config?)
```

## 在组件中使用

```vue
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getUserInfo } from '@/api/user';
import type { UserInfo } from '@/api/user';

const userInfo = ref<UserInfo | null>(null);
const loading = ref(false);

const fetchUserInfo = async () => {
  try {
    loading.value = true;
    const response = await getUserInfo();
    userInfo.value = response.data;
  } catch (error) {
    console.error('获取用户信息失败:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchUserInfo();
});
</script>
```

## Token 管理

### 存储 Token

```typescript
import { setToken } from '@/utils/auth';

// 登录成功后存储 token
const response = await login(loginData);
setToken(response.data.access_token);
```

### 获取 Token

```typescript
import { getToken, hasToken } from '@/utils/auth';

// 检查是否有 token
if (hasToken()) {
  // 有 token，可以进行需要认证的操作
}
```

### 清除 Token

```typescript
import { removeToken } from '@/utils/auth';

// 登出时清除 token
removeToken();
```

## 最佳实践

1. **接口集中管理**: 按业务模块创建独立的 API 文件
2. **类型定义**: 为所有接口参数和返回值定义 TypeScript 类型
3. **错误处理**: 在组件中使用 try-catch 处理业务逻辑错误
4. **加载状态**: 在调用接口时合理使用 loading 状态
5. **缓存策略**: 对于不常变化的数据，考虑添加缓存机制

## 注意事项

- 所有接口都会自动处理 HTTP 错误和业务错误
- Token 失效会自动跳转到登录页面
- 请求超时时间为 12 秒
- 开发环境中会打印详细的错误信息 