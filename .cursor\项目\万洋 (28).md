---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心前端/项目

<a id="opIdedit"></a>

## PUT 修改项目

PUT /project

修改项目

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "type": 0,
  "mdmProjectId": "string",
  "mdmName": "string",
  "mdmSaleName": "string",
  "code": "string",
  "name": "string",
  "provinceCode": "string",
  "provinceName": "string",
  "cityCode": "string",
  "cityName": "string",
  "countryCode": "string",
  "countryName": "string",
  "mdmTypeName": "string",
  "assetType": 0,
  "propertyUnit": "string",
  "projectAddress": "string",
  "longitude": "string",
  "latitude": "string",
  "totalSelfArea": "string",
  "isDel": true,
  "stageList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "mdmStageId": "string",
      "projectId": "string",
      "stageName": "string",
      "isDel": true
    }
  ],
  "parcelList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "mdmParcelId": "string",
      "projectId": "string",
      "stageId": "string",
      "stageName": "string",
      "parcelName": "string",
      "mdmParcelName": "string",
      "mdmNatureName": "string",
      "landUsage": 0,
      "mdmAddress": "string",
      "address": "string",
      "totalSelfArea": "string",
      "isDel": true
    }
  ],
  "buildingList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "source": 0,
      "mdmBuildingId": "string",
      "projectId": "string",
      "parcelId": "string",
      "buildingName": "string",
      "upFloorNums": 0,
      "underFloorNums": 0,
      "totalSelfArea": "string",
      "certificateBuildingName": "string",
      "isDel": true
    }
  ],
  "deleteStageIdList": [
    "string"
  ],
  "deleteParcelIdList": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SysProjectAddDTO](#schemasysprojectadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdadd"></a>

## POST 新增项目

POST /project

新增项目

> Body 请求参数

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "type": 0,
  "mdmProjectId": "string",
  "mdmName": "string",
  "mdmSaleName": "string",
  "code": "string",
  "name": "string",
  "provinceCode": "string",
  "provinceName": "string",
  "cityCode": "string",
  "cityName": "string",
  "countryCode": "string",
  "countryName": "string",
  "mdmTypeName": "string",
  "assetType": 0,
  "propertyUnit": "string",
  "projectAddress": "string",
  "longitude": "string",
  "latitude": "string",
  "totalSelfArea": "string",
  "isDel": true,
  "stageList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "mdmStageId": "string",
      "projectId": "string",
      "stageName": "string",
      "isDel": true
    }
  ],
  "parcelList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "mdmParcelId": "string",
      "projectId": "string",
      "stageId": "string",
      "stageName": "string",
      "parcelName": "string",
      "mdmParcelName": "string",
      "mdmNatureName": "string",
      "landUsage": 0,
      "mdmAddress": "string",
      "address": "string",
      "totalSelfArea": "string",
      "isDel": true
    }
  ],
  "buildingList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "source": 0,
      "mdmBuildingId": "string",
      "projectId": "string",
      "parcelId": "string",
      "buildingName": "string",
      "upFloorNums": 0,
      "underFloorNums": 0,
      "totalSelfArea": "string",
      "certificateBuildingName": "string",
      "isDel": true
    }
  ],
  "deleteStageIdList": [
    "string"
  ],
  "deleteParcelIdList": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SysProjectAddDTO](#schemasysprojectadddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdexport"></a>

## POST 导出询项目列表

POST /project/export

导出询项目列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|params|query|object| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|id|query|string| 否 | 主键ID|主键ID|
|parentId|query|string| 否 | 集团/区域/片区ID|集团/区域/片区ID|
|projectName|query|string| 否 | 项目名称|项目名称|
|projectIdList|query|array[string]| 否 ||none|
|Authorization|header|string| 否 ||none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIdparcelList"></a>

## GET 地块下拉列表接口

GET /project/parcel/list

查询项目下所有地块信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|projectId|query|string| 是 ||项目ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
[{"createBy":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateBy":"string","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","id":"string","mdmParcelId":"string","projectId":"string","stageId":"string","parcelName":"string","mdmNatureName":"string","landUsage":0,"mdmAddress":"string","address":"string","totalSelfArea":"string","isDel":true}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|地块列表|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[SysParcel](#schemasysparcel)]|false|none||[地块信息]|
|» 地块信息|[SysParcel](#schemasysparcel)|false|none|地块信息|地块信息|
|»» createBy|string|false|none||none|
|»» createByName|string|false|none||none|
|»» createTime|string(date-time)|false|none||none|
|»» updateBy|string|false|none||none|
|»» updateByName|string|false|none||none|
|»» updateTime|string(date-time)|false|none||none|
|»» id|string|false|none||none|
|»» mdmParcelId|string|false|none||none|
|»» projectId|string|false|none||none|
|»» stageId|string|false|none||none|
|»» parcelName|string|false|none||none|
|»» mdmNatureName|string|false|none||none|
|»» landUsage|integer(int32)|false|none||none|
|»» mdmAddress|string|false|none||none|
|»» address|string|false|none||none|
|»» totalSelfArea|string|false|none||none|
|»» isDel|boolean|false|none||none|

<a id="opIdlist_3"></a>

## GET 查询项目列表

GET /project/list

查询项目列表

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 | 主键ID|主键ID|
|parentId|query|string| 否 | 集团/区域/片区ID|集团/区域/片区ID|
|projectName|query|string| 否 | 项目名称|项目名称|
|projectIdList|query|array[string]| 否 ||none|
|pageNum|query|integer(int32)| 是 ||none|
|pageSize|query|integer(int32)| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|

<a id="opIdfloorList"></a>

## GET 楼层下拉列表接口

GET /project/floor/list

查询楼栋下所有楼层信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|buildingId|query|string| 是 ||楼栋ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
[{"createBy":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateBy":"string","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","id":"string","mdmFloorId":"string","projectId":"string","buildingId":"string","floorName":"string","floorTypeName":"string","designFloorHeight":"string","designLoad":"string","isDel":true}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|楼层列表|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[SysFloor](#schemasysfloor)]|false|none||none|
|» createBy|string|false|none||none|
|» createByName|string|false|none||none|
|» createTime|string(date-time)|false|none||none|
|» updateBy|string|false|none||none|
|» updateByName|string|false|none||none|
|» updateTime|string(date-time)|false|none||none|
|» id|string|false|none||none|
|» mdmFloorId|string|false|none||none|
|» projectId|string|false|none||none|
|» buildingId|string|false|none||none|
|» floorName|string|false|none||none|
|» floorTypeName|string|false|none||none|
|» designFloorHeight|string|false|none||none|
|» designLoad|string|false|none||none|
|» isDel|boolean|false|none||none|

<a id="opIdgetInfo"></a>

## GET 获取项目详细信息

GET /project/detail

获取项目详细信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdbuildingSelectList"></a>

## GET 楼栋下拉列表接口

GET /project/building/selectList

查询地块下所有楼栋信息

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|parcelId|query|string| 是 ||地块ID|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
[{"createBy":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateBy":"string","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","id":"string","source":0,"mdmBuildingId":"string","projectId":"string","parcelId":"string","buildingName":"string","mdmBuildingName":"string","certificateBuildingName":"string","upFloorNums":0,"underFloorNums":0,"totalSelfArea":"string","isDel":true}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|楼栋列表|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[SysBuilding](#schemasysbuilding)]|false|none||none|
|» createBy|string|false|none||none|
|» createByName|string|false|none||none|
|» createTime|string(date-time)|false|none||none|
|» updateBy|string|false|none||none|
|» updateByName|string|false|none||none|
|» updateTime|string(date-time)|false|none||none|
|» id|string|false|none||none|
|» source|integer(int32)|false|none||none|
|» mdmBuildingId|string|false|none||none|
|» projectId|string|false|none||none|
|» parcelId|string|false|none||none|
|» buildingName|string|false|none||none|
|» mdmBuildingName|string|false|none||none|
|» certificateBuildingName|string|false|none||none|
|» upFloorNums|integer(int32)|false|none||none|
|» underFloorNums|integer(int32)|false|none||none|
|» totalSelfArea|string|false|none||none|
|» isDel|boolean|false|none||none|

<a id="opIdremove"></a>

## DELETE 删除项目

DELETE /project/delete

删除项目

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|ids|query|array[string]| 是 ||none|
|Authorization|header|string| 否 ||none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_SysBuildingAddDTO">SysBuildingAddDTO</h2>

<a id="schemasysbuildingadddto"></a>
<a id="schema_SysBuildingAddDTO"></a>
<a id="tocSsysbuildingadddto"></a>
<a id="tocssysbuildingadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "source": 0,
  "mdmBuildingId": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingName": "string",
  "upFloorNums": 0,
  "underFloorNums": 0,
  "totalSelfArea": "string",
  "certificateBuildingName": "string",
  "isDel": true
}

```

楼栋列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|source|integer(int32)|false|none|来源（1主数据 2手动添加）|来源（1主数据 2手动添加）|
|mdmBuildingId|string|false|none|主数据楼栋id|主数据楼栋id|
|projectId|string|false|none|项目id|项目id|
|parcelId|string|false|none|所属地块id|所属地块id|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|upFloorNums|integer(int32)|false|none|地上楼层数|地上楼层数|
|underFloorNums|integer(int32)|false|none|地下楼层数|地下楼层数|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|certificateBuildingName|string|false|none||none|
|isDel|boolean|false|none|是否删除（0否 1是）|是否删除（0否 1是）|

<h2 id="tocS_SysParcelAddDTO">SysParcelAddDTO</h2>

<a id="schemasysparceladddto"></a>
<a id="schema_SysParcelAddDTO"></a>
<a id="tocSsysparceladddto"></a>
<a id="tocssysparceladddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "mdmParcelId": "string",
  "projectId": "string",
  "stageId": "string",
  "stageName": "string",
  "parcelName": "string",
  "mdmParcelName": "string",
  "mdmNatureName": "string",
  "landUsage": 0,
  "mdmAddress": "string",
  "address": "string",
  "totalSelfArea": "string",
  "isDel": true
}

```

地块列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|mdmParcelId|string|false|none|主数据地块id|主数据地块id|
|projectId|string|false|none|项目id|项目id|
|stageId|string|false|none|分期id|分期id|
|stageName|string|false|none|分期名称|分期名称|
|parcelName|string|false|none|地块名称|地块名称|
|mdmParcelName|string|false|none||none|
|mdmNatureName|string|false|none|主数据用地性质名称|主数据用地性质名称|
|landUsage|integer(int32)|false|none|用地性质（1工业用地 2商业用地）|用地性质（1工业用地 2商业用地）|
|mdmAddress|string|false|none|主数据地址|主数据地址|
|address|string|false|none|地址|地址|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|isDel|boolean|false|none|是否删除（0否 1是）|是否删除（0否 1是）|

<h2 id="tocS_SysProjectAddDTO">SysProjectAddDTO</h2>

<a id="schemasysprojectadddto"></a>
<a id="schema_SysProjectAddDTO"></a>
<a id="tocSsysprojectadddto"></a>
<a id="tocssysprojectadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "type": 0,
  "mdmProjectId": "string",
  "mdmName": "string",
  "mdmSaleName": "string",
  "code": "string",
  "name": "string",
  "provinceCode": "string",
  "provinceName": "string",
  "cityCode": "string",
  "cityName": "string",
  "countryCode": "string",
  "countryName": "string",
  "mdmTypeName": "string",
  "assetType": 0,
  "propertyUnit": "string",
  "projectAddress": "string",
  "longitude": "string",
  "latitude": "string",
  "totalSelfArea": "string",
  "isDel": true,
  "stageList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "mdmStageId": "string",
      "projectId": "string",
      "stageName": "string",
      "isDel": true
    }
  ],
  "parcelList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "mdmParcelId": "string",
      "projectId": "string",
      "stageId": "string",
      "stageName": "string",
      "parcelName": "string",
      "mdmParcelName": "string",
      "mdmNatureName": "string",
      "landUsage": 0,
      "mdmAddress": "string",
      "address": "string",
      "totalSelfArea": "string",
      "isDel": true
    }
  ],
  "buildingList": [
    {
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z",
      "id": "string",
      "source": 0,
      "mdmBuildingId": "string",
      "projectId": "string",
      "parcelId": "string",
      "buildingName": "string",
      "upFloorNums": 0,
      "underFloorNums": 0,
      "totalSelfArea": "string",
      "certificateBuildingName": "string",
      "isDel": true
    }
  ],
  "deleteStageIdList": [
    "string"
  ],
  "deleteParcelIdList": [
    "string"
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|type|integer(int32)|false|none|项目类型（1内部 2外部）|项目类型（1内部 2外部）|
|mdmProjectId|string|false|none|主数据项目ID|主数据项目ID|
|mdmName|string|false|none|主数据项目名称|主数据项目名称|
|mdmSaleName|string|false|none|主数据销售推广名|主数据销售推广名|
|code|string|false|none|编码|编码|
|name|string|false|none|简称|简称|
|provinceCode|string|false|none|省编码|省编码|
|provinceName|string|false|none|省名称|省名称|
|cityCode|string|false|none|市编码|市编码|
|cityName|string|false|none|市名称|市名称|
|countryCode|string|false|none|区编码|区编码|
|countryName|string|false|none|区名称|区名称|
|mdmTypeName|string|false|none|主数据项目类型|主数据项目类型|
|assetType|integer(int32)|false|none|项目资产分类（1众创城 2科技城 3产城 4外部项目）|项目资产分类（1众创城 2科技城 3产城 4外部项目）|
|propertyUnit|string|false|none|产权单位|产权单位|
|projectAddress|string|false|none|项目地址|项目地址|
|longitude|string|false|none|地址经度|地址经度|
|latitude|string|false|none|地址维度|地址维度|
|totalSelfArea|string|false|none|总自持面积|总自持面积|
|isDel|boolean|false|none|0 否 1是|0 否 1是|
|stageList|[[SysStageAddDTO](#schemasysstageadddto)]|false|none|分期列表|分期列表|
|parcelList|[[SysParcelAddDTO](#schemasysparceladddto)]|false|none|地块列表|地块列表|
|buildingList|[[SysBuildingAddDTO](#schemasysbuildingadddto)]|false|none|楼栋列表|楼栋列表|
|deleteStageIdList|[string]|false|none|删除的分期Id列表|删除的分期Id列表|
|» 删除的分期Id列表|string|false|none|删除的分期Id列表|删除的分期Id列表|
|deleteParcelIdList|[string]|false|none|删除的地块Id列表|删除的地块Id列表|
|» 删除的地块Id列表|string|false|none|删除的地块Id列表|删除的地块Id列表|

<h2 id="tocS_SysStageAddDTO">SysStageAddDTO</h2>

<a id="schemasysstageadddto"></a>
<a id="schema_SysStageAddDTO"></a>
<a id="tocSsysstageadddto"></a>
<a id="tocssysstageadddto"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "mdmStageId": "string",
  "projectId": "string",
  "stageName": "string",
  "isDel": true
}

```

分期列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none|主键ID|主键ID|
|mdmStageId|string|false|none|主数据分期id|主数据分期id|
|projectId|string|false|none|项目id|项目id|
|stageName|string|false|none|分期名称|分期名称|
|isDel|boolean|false|none|0 否 1是|0 否 1是|

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "total": 0,
  "rows": [
    {}
  ],
  "code": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|rows|[object]|false|none||none|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_SysParcel">SysParcel</h2>

<a id="schemasysparcel"></a>
<a id="schema_SysParcel"></a>
<a id="tocSsysparcel"></a>
<a id="tocssysparcel"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "mdmParcelId": "string",
  "projectId": "string",
  "stageId": "string",
  "parcelName": "string",
  "mdmNatureName": "string",
  "landUsage": 0,
  "mdmAddress": "string",
  "address": "string",
  "totalSelfArea": "string",
  "isDel": true
}

```

地块信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none||none|
|mdmParcelId|string|false|none||none|
|projectId|string|false|none||none|
|stageId|string|false|none||none|
|parcelName|string|false|none||none|
|mdmNatureName|string|false|none||none|
|landUsage|integer(int32)|false|none||none|
|mdmAddress|string|false|none||none|
|address|string|false|none||none|
|totalSelfArea|string|false|none||none|
|isDel|boolean|false|none||none|

<h2 id="tocS_SysFloor">SysFloor</h2>

<a id="schemasysfloor"></a>
<a id="schema_SysFloor"></a>
<a id="tocSsysfloor"></a>
<a id="tocssysfloor"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "mdmFloorId": "string",
  "projectId": "string",
  "buildingId": "string",
  "floorName": "string",
  "floorTypeName": "string",
  "designFloorHeight": "string",
  "designLoad": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none||none|
|mdmFloorId|string|false|none||none|
|projectId|string|false|none||none|
|buildingId|string|false|none||none|
|floorName|string|false|none||none|
|floorTypeName|string|false|none||none|
|designFloorHeight|string|false|none||none|
|designLoad|string|false|none||none|
|isDel|boolean|false|none||none|

<h2 id="tocS_SysBuilding">SysBuilding</h2>

<a id="schemasysbuilding"></a>
<a id="schema_SysBuilding"></a>
<a id="tocSsysbuilding"></a>
<a id="tocssysbuilding"></a>

```json
{
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "id": "string",
  "source": 0,
  "mdmBuildingId": "string",
  "projectId": "string",
  "parcelId": "string",
  "buildingName": "string",
  "mdmBuildingName": "string",
  "certificateBuildingName": "string",
  "upFloorNums": 0,
  "underFloorNums": 0,
  "totalSelfArea": "string",
  "isDel": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|string|false|none||none|
|createByName|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|updateBy|string|false|none||none|
|updateByName|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|id|string|false|none||none|
|source|integer(int32)|false|none||none|
|mdmBuildingId|string|false|none||none|
|projectId|string|false|none||none|
|parcelId|string|false|none||none|
|buildingName|string|false|none||none|
|mdmBuildingName|string|false|none||none|
|certificateBuildingName|string|false|none||none|
|upFloorNums|integer(int32)|false|none||none|
|underFloorNums|integer(int32)|false|none||none|
|totalSelfArea|string|false|none||none|
|isDel|boolean|false|none||none|

