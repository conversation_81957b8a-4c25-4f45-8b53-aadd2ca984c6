import http from './index'

// 房源选项接口定义（基于实际返回的数据结构）
export interface RoomOption {
    id: string | null
    name: string | null
    parentId: string | null
    roomId: string | null
    roomName: string | null
    projectId: string | null
    projectName: string | null
    stageId: string | null
    stageName: string | null
    parcelId: string | null
    parcelName: string | null
    buildingId: string | null
    buildingName: string | null
    floorId: string | null
    floorName: string | null
    propertyType: string | null
    rentStatus: string | null
    rentAreaType: number | null
    rentArea: number | null
    price: number | null
    bottomPrice: number | null
    priceUnit: number | null
    externalRentStartDate: string | null
    depositType: number | null
    depositAmount: number | null
    level: number
    children: RoomOption[] | null
}

// 房源选项查询参数
export interface RoomOptionsParams {
    projectId: string
    buildingType?: string // 业态类型（对应意向业态）
    businessType?: string // 业态类型（兼容性保留）
    keyword?: string // 搜索关键词
    status?: string | number // 房源状态
    pageNum?: number
    pageSize?: number
}

// 房源选项响应数据（树形结构）
export interface RoomOptionsResponse extends Array<RoomOption> {}

// 房态图查询参数（根据接口文档RoomDiagramQueryDTO）
export interface RoomDiagramQueryDTO {
    projectId?: string
    parcelId?: string
    buildingId?: string
    floorId?: string
    roomStatus?: number // 房源状态(1.空置，2.在租，3.待生效/签约中/已预定， 4.不可招商)
    propertyType?: string // 房源用途
    diagramDate?: string // 指定房态时间，默认为当前时间
    dueSoon?: boolean // 是否即将到期: true-是，false-否
    isSelfUse?: boolean // 是否自用: true-是，false-否
    isLock?: boolean // 是否锁房: true-是，false-否
    isDirty?: boolean // 是否脏房: true-是，false-否
    isMaintain?: boolean // 是否维修: true-是，false-否
}

// 订单信息（BookDiagramVo）
export interface BookDiagramVo {
    bookingId: string | null
    customerName: string | null
    companyName: string | null
    bookingAmount: number | null
    bookingTime: string | null
    canRefund: boolean | null
    roomStatus: string | null
}

// 合同信息（ContractDiagramVo）
export interface ContractDiagramVo {
    roomId: string | null
    contractId: string | null
    contractNo: string | null
    contractNumber: string | null
    contractCategory: string | null
    contractType: number | null
    signType: number | null
    rentPrice: number | null
    tenantType: string | null
    tenantName: string | null
    tenantIdCard: string | null
    monthlyPrice: number | null
    startDate: string | null
    endDate: string | null
    rentTerm: string | null
    status: number | null
    roomStatus: string | null
    approveStatus: number | null
    terminateId: string | null
}

// 房间房态信息（RoomDiagramVo）
export interface RoomDiagramVo {
    roomId: string
    roomName: string
    type: number // 房源类型（1普通 2多经）
    propertyType: string
    propertyTypeName: string
    rentArea: number
    orientation: string
    orientationName: string
    isSelfUse: boolean
    needCheckIn: boolean
    needCheckOut: boolean
    roomStatusName: string
    roomStatus: number // 房源状态(1.空置，2.在租，3.待生效/签约中/已预定， 4.不可招商)
    tags: string[] // 房源标识列表，如：已预订、签约中、待生效、未进场、即将到期、未出场
    houseTypeId: string
    tablePrice: number
    priceUnit: number
    baseRent: number
    additionalFee: number
    rentAreaType: number
    emptyDays: number
    selfUseSubject: number
    rentalStartDate: string | null
    externalRentStartDate: string | null
    isDirty: boolean
    isLock: boolean
    isMaintain: boolean
    bookingVo: BookDiagramVo | null
    bookings: BookDiagramVo[]
    contractVo: ContractDiagramVo | null
    contracts: ContractDiagramVo[]
}

// 楼层房态图（FloorDiagramVo）
export interface FloorDiagramVo {
    floorId: string
    floorName: string
    floorSort: number
    rooms: RoomDiagramVo[]
}

// 房态图响应数据（DiagramVo）
export interface DiagramVo {
    totalCount: number // 总数
    emptyCount: number // 空置数
    rentCount: number // 在租数
    toEffectCount: number // 待生效/签约中/已预定总数
    invalidCount: number // 不可招商数
    floorDiagramList: FloorDiagramVo[] // 楼层房态图
    propertyList: string[] // 用途列表
}

// 房间树查询参数（根据接口文档RoomTreeQueryDTO）
export interface RoomTreeQueryDTO {
    params?: Record<string, any>
    pageNum: number
    pageSize: number
    contractType?: string // 合同类型，0-非宿舍,1-宿舍,2-多经,3-日租房
    buildingType?: string // 业态
    buildingId?: string // 楼栋id
    rentStatus?: string // 租控状态 0-可租 1-已租
    roomName?: string // 房源名称
    projectId?: string // 项目id
    pricingFlag?: number // 是否定价 0:否，1：是
}

/**
 * 根据项目ID获取房源选项
 * @param params 查询参数，其中buildingType对应意向业态类型用于筛选房源
 * @returns 房源选项树形列表
 */
export const getRoomOptions = (params: RoomOptionsParams) => {
    return http.post<RoomOptionsResponse>('/business-rent-admin/room/roomOptions', params)
}

/**
 * 获取房态简图数据
 * @param params 查询参数
 * @returns 房态简图数据
 */
export const getRoomDiagram = (params: RoomDiagramQueryDTO) => {
    return http.post<DiagramVo>('/business-rent-rest/room/simple/diagram', params)
}

/**
 * 获取房态简图数据（兼容性接口）
 * @param params 查询参数
 * @returns 房态简图数据
 */
export const getRoomSimpleDiagram = (params: RoomDiagramQueryDTO) => {
    return getRoomDiagram(params)
}

/**
 * 查询用户有权限的房间树
 * @param params 查询参数
 * @returns 房间树形结构
 */
export const getRoomTree = (params: RoomTreeQueryDTO) => {
    return http.post<RoomOption[]>('/business-rent-rest/room/roomOptions', params)
}

/**
 * 根据项目ID获取房源树形结构（兼容旧接口）
 * @param projectId 项目ID
 * @param businessType 业态类型（可选）
 * @returns 房源树形数据
 */
export const getRoomTreeByProject = (projectId: string, businessType?: string) => {
    const params: any = { 
        projectId,
        pageNum: 1,
        pageSize: 1000
    }
    if (businessType) {
        params.buildingType = businessType
    }
    return getRoomTree(params)
}

/**
 * 根据房源ID获取房源详情
 * @param roomId 房源ID
 * @returns 房源详情
 */
export const getRoomDetail = (roomId: string) => {
    return http.get<RoomOption>(`/business-rent-admin/room/${roomId}`)
}

/**
 * 从树形结构中提取所有实际房源（level: 4）
 * @param treeData 树形数据
 * @returns 扁平化的房源列表
 */
export const extractRoomsFromTree = (treeData: RoomOption[]): RoomOption[] => {
    const rooms: RoomOption[] = []
    
    const traverse = (nodes: RoomOption[]) => {
        for (const node of nodes) {
            // level: 4 表示实际的房源
            if (node.level === 4 && node.roomId && node.roomName) {
                rooms.push(node)
            }
            
            // 递归处理子节点
            if (node.children && node.children.length > 0) {
                traverse(node.children)
            }
        }
    }
    
    traverse(treeData)
    return rooms
} 