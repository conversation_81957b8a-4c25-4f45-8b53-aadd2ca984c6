<template>
    <div class="refund-booking-page">
        <!-- 顶部导航 -->
        <van-nav-bar 
            title="退定" 
            left-arrow 
            @click-left="$router.go(-1)"
            class="custom-nav-bar"
        />
        <!-- 页面内容 -->
        <div class="page-content">
            <!-- 定单信息区块 -->
            <div class="info-block">
                <moduleTitle title="定单信息" status="" :tag="true"></moduleTitle>
                
                <div class="info-content">
                    <div class="info-item">
                        <span class="info-label">客户姓名：</span>
                        <span class="info-value">{{ bookingInfo.customerName }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">意向房源：</span>
                        <span class="info-value">{{ bookingInfo.roomName }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">定金金额：</span>
                        <span class="info-value">{{ bookingInfo.bookingAmount }}元</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">已收定金：</span>
                        <span class="info-value">{{ bookingInfo.receivedAmount }}元</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">缴纳日期：</span>
                        <span class="info-value">{{ bookingInfo.receivedDate }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">是否退定金：</span>
                        <span class="info-value">{{ bookingInfo.isRefundable === 1 ? '是' : '否' }}</span>
                    </div>
                </div>
            </div>

            <!-- 退定信息区块 -->
            <div class="info-block" v-if="bookingInfo.isRefundable === 0">
                <moduleTitle title="退定信息" status="" :tag="true"></moduleTitle>
                
                <div class="info-content">
                    <!-- 退定说明 -->
                    <van-field
                        v-model="refundForm.cancelRemark"
                        label="退定说明"
                        type="textarea"
                        placeholder="请输入退定说明"
                        rows="4"
                        autosize
                        class="refund-remark"
                    />
                    <!-- 退定附件 -->
                    <div class="upload-section">
                        <div class="upload-title">退定附件</div>
                        <van-uploader
                            v-model="uploadedFiles"
                            :max-count="5"
                            :after-read="afterRead"
                            @delete="onDelete"
                        />
                    </div>

                    <!-- 是否退款 -->
                    <!-- <van-field
                        v-model="refundForm.isRefund"
                        is-link
                        readonly
                        label="是否退款"
                        placeholder="请选择"
                        @click="showRefundPicker = true"
                    />
                    <van-popup v-model:show="showRefundPicker" position="bottom">
                        <van-picker
                            :columns="refundColumns"
                            @confirm="onRefundConfirm"
                            @cancel="showRefundPicker = false"
                        />
                    </van-popup> -->
                </div>
            </div>

            <!-- 温馨提示区块（当可退定金时显示） -->
            <div class="info-block" v-if="bookingInfo.isRefundable === 1">
                <moduleTitle title="温馨提示" status="" :tag="true"></moduleTitle>
                
                <div class="info-content">
                    <div class="tip-content">
                        <van-icon name="info-o" class="tip-icon" />
                        <div class="tip-text">
                            如需退定金，请在电脑端发起退定及退款申请
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部按钮（仅在不可退定金时显示） -->
        <div class="bottom-buttons" v-if="bookingInfo.isRefundable === 0">
            <van-button class="save-btn" @click="handleSave">保存</van-button>
            <van-button type="primary" class="submit-btn" @click="handleSubmit">提交</van-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import moduleTitle from '@/components/ModuleTitle.vue'
import { getBookingBasicInfo, cancelBooking } from '@/api/booking'
import { uploadImage, type UploadResponse } from '../api/exit'
import { compressImage, validateImageFile } from '../utils/upload'

const router = useRouter()
const route = useRoute()

// 定单基本信息
const bookingInfo = ref({
    customerName: '',
    roomName: '',
    bookingAmount: 0,
    receivedAmount: 0,
    isRefundable: 0,
    receivedDate: ''
})

// 退定表单
const refundForm = reactive({
    id: '',
    cancelRemark: '',
    cancelEnclosure: '',
    isRefund: '否',
    isSubmit: 0
})

// 上传文件
const uploadedFiles = ref<any[]>([])

// 退款选择
const showRefundPicker = ref(false)
const refundColumns = [
    { text: '是', value: '是' },
    { text: '否', value: '否' }
]

// 初始化
onMounted(async () => {
    const id = route.params.id
    if (id) {
        refundForm.id = id as string
        await loadBookingInfo(id as string)
    } else {
        showToast('缺少定单ID')
        router.back()
    }
})

// 加载定单信息
const loadBookingInfo = async (id: string) => {
    try {
        const res = await getBookingBasicInfo(id)
        if (res.code === 200) {
            bookingInfo.value = res.data
            refundForm.cancelRemark = res.data.cancelRemark
            refundForm.cancelEnclosure = res.data.cancelEnclosure
            if(res.data.cancelEnclosure){
                let cancelEnclosure = JSON.parse(res.data.cancelEnclosure)  
                uploadedFiles.value = [
                    {
                        url: cancelEnclosure.fileUrl,
                        status: 'done'
                    }
                ]
            }
            // refundForm.isRefund = res.data.isRefund
            // refundForm.isSubmit = res.data.isSubmit/*  */
        }
    } catch (error) {
        console.error('获取定单信息失败:', error)
        showToast('获取定单信息失败')
    }
}

// 退款选择确认
const onRefundConfirm = (value: any) => {
    refundForm.isRefund = value.selectedOptions[0]?.text || value
    showRefundPicker.value = false
}

// 上传文件后的处理
const afterRead = async (file: any) => {
    try {
        // 设置上传状态
        file.status = 'uploading'
        file.message = '上传中...'
        
        // 验证文件
        const validation = validateImageFile(file.file)
        if (!validation.valid) {
            file.status = 'failed'
            file.message = validation.message
            showToast(validation.message || '文件验证失败')
            return
        }
        
        // 压缩图片
        const compressedBlob = await compressImage(file.file, 0.8, 1200, 1200)
        
        // 上传文件
        const uploadResult = await uploadImage(compressedBlob)
        const uploadResponse: UploadResponse = uploadResult.data
        
        // 更新文件状态
        file.status = 'done'
        file.message = '上传成功'
        file.url = uploadResponse.fileUrl || uploadResponse.url
        file.uploadResponse = uploadResponse
        
        // 更新表单的附件字段（存储文件URL数组）
        const fileUrls = uploadedFiles.value
            .filter((f: any) => f.status === 'done' && f.url)
            .map((f: any) => f.url)
        // refundForm.cancelEnclosure = fileUrls.join(',')

        refundForm.cancelEnclosure = JSON.stringify(uploadResponse)
        
        showToast({
            type: 'success',
            message: '文件上传成功'
        })
    } catch (error) {
        file.status = 'failed'
        file.message = '上传失败'
        console.error('文件上传失败:', error)
        showToast({
            type: 'fail',
            message: '文件上传失败，请重试'
        })
    }
}

// 删除文件
const onDelete = (file: any) => {
    const index = uploadedFiles.value.indexOf(file)
    if (index !== -1) {
        uploadedFiles.value.splice(index, 1)
        
        // 重新生成附件字段（仅包含成功上传的文件URL）
        const fileUrls = uploadedFiles.value
            .filter((f: any) => f.status === 'done' && f.url)
            .map((f: any) => f.url)
        refundForm.cancelEnclosure = ''
        
        showToast('文件已删除')
    }
}

// 表单验证
const validateForm = () => {
    if (!refundForm.cancelRemark) {
        showToast('请输入退定说明')
        return false
    }
    return true
}

// 保存
const handleSave = async () => {
    if (!validateForm()) return
    
    try {
        const params = {
            ...refundForm,
            isSubmit: 0,
            isRefund: refundForm.isRefund === '是' ? 1 : 0
        }
        const res = await cancelBooking(params)
        if (res.code === 200) {
            showToast('保存成功')
            setTimeout(() => {
                router.back()
            }, 1000)
        }
    } catch (error) {
        console.error('保存失败:', error)
        showToast('保存失败')
    }
}

// 提交
const handleSubmit = async () => {
    if (!validateForm()) return
    
    try {
        const params = {
            ...refundForm,
            isSubmit: 1,
            isRefund: refundForm.isRefund === '是' ? 1 : 0
        }
        const res = await cancelBooking(params)
        if (res.code === 200) {
            showToast('提交成功')
            setTimeout(() => {
                router.back()
            }, 1000)
        }
    } catch (error) {
        console.error('提交失败:', error)
        showToast('提交失败')
    }
}
</script>

<style scoped>
.refund-booking-page {
    background-color: #f1f1f1;
    min-height: 100vh;
    padding-bottom: 80px;
}

/* 导航栏样式 */
.custom-nav-bar {
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
}

/* 页面内容 */
.page-content {
    padding: 20px;
}

/* 信息区块 */
.info-block {
    background-color: #fff;
    border-radius: 20px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 信息内容 */
.info-content {
    padding: 23px 20px 20px 20px;
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 24px;
    line-height: 33px;
}

.info-item:first-child {
    margin-top: 0;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    color: #919199;
    min-width: 140px;
    flex-shrink: 0;
}

.info-value {
    color: #333;
    flex: 1;
}

/* 退定说明 */
.refund-remark {
    margin-bottom: 20px;
}

/* 上传区域 */
.upload-section {
    margin-bottom: 20px;
}

.upload-title {
    margin-bottom: 10px;
    font-size: 28px;
    color: #646566;
}

/* 底部按钮 */
.bottom-buttons {
    display: flex;
    gap: 30px;
    padding: 30px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #FFFFFF;

    .save-btn {
        flex: 1;
        border: 1px solid #3583FF;
        color: #3583FF;
        font-weight: 500;
        border-radius: 60px;
    }

    .submit-btn {
        flex: 1;
        border: 1px solid #3583FF;
        color: #fff;
        font-weight: 500;
        border-radius: 60px;
        background: #3583FF;
    }

}

/* 温馨提示样式 */
.tip-content {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    background-color: #fff8e1;
    border-radius: 12px;
    border-left: 4px solid #ff9800;
}

.tip-icon {
    font-size: 32px;
    color: #ff9800;
    margin-right: 16px;
    margin-top: 2px;
    flex-shrink: 0;
}

.tip-text {
    font-size: 28px;
    color: #e65100;
    line-height: 1.5;
    font-weight: 500;
}
</style> 