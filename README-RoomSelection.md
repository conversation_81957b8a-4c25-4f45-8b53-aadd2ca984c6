# 进场房源选择功能

## 功能概述

进场房源选择功能是进场管理页面的核心功能之一，允许用户在办理进场时选择具体的房源。

## 功能特性

### 1. 房源选择弹框
- **触发方式**：点击进场管理列表中的"办理进场"按钮
- **弹框高度**：占屏幕85%高度
- **位置**：从底部弹出
- **关闭方式**：点击右上角关闭按钮或确认操作

### 2. 搜索功能
- **实时搜索**：输入内容时实时过滤房源列表
- **搜索范围**：房源名称、楼栋名称
- **搜索方式**：模糊匹配，不区分大小写
- **清空搜索**：点击搜索框的清空按钮恢复完整列表

### 3. 房源列表
- **数据来源**：调用 `getEntryRooms` API获取指定进场通知单的房源
- **过滤条件**：只显示未进场的房源（`isEntered: false`）
- **显示内容**：房源名称（如：北区-56幢-101）
- **选择方式**：单选框形式，支持多选

### 4. 全选功能
- **全选按钮**：位于房源列表顶部，灰色背景突出显示
- **智能切换**：
  - 未全选时：点击执行全选
  - 已全选时：点击取消全选
- **状态同步**：个别选择达到全部时，全选按钮自动选中

### 5. 确认操作
- **按钮状态**：
  - 未选择房源：按钮置灰禁用
  - 已选择房源：显示蓝色可点击，并显示选择数量
- **确认流程**：
  1. 弹出确认对话框，显示选择的房源数量
  2. 用户确认后调用 `handleEntry` API
  3. 成功后关闭弹框并刷新列表

## 交互流程

```
1. 用户点击"办理进场"按钮
   ↓
2. 弹出房源选择弹框
   ↓
3. 加载该进场通知单的待进场房源
   ↓
4. 用户搜索/选择房源
   ↓
5. 点击确定按钮
   ↓
6. 确认对话框
   ↓
7. 调用办理进场API
   ↓
8. 成功后关闭弹框，刷新列表
```

## API接口

### 获取房源列表
```typescript
getEntryRooms(entryId: string)
```
- **参数**：进场通知单ID
- **返回**：房源信息数组，包含房源ID、名称、楼栋等信息

### 办理进场
```typescript
handleEntry({
  entryId: string,    // 进场通知单ID
  roomIds: string[]   // 选择的房源ID数组
})
```

## 错误处理

### 1. API异常处理
- **获取房源失败**：显示错误提示，使用模拟数据作为降级方案
- **办理进场失败**：显示错误提示，不关闭弹框

### 2. 用户操作验证
- **未选择房源**：点击确定时提示"请选择要办理进场的房源"
- **网络异常**：显示相应错误信息

## 样式特点

### 1. 布局结构
- **顶部**：标题栏（选择进场房源 + 关闭按钮）
- **搜索区**：搜索输入框
- **列表区**：可滚动的房源列表（占据主要空间）
- **底部**：确认按钮

### 2. 视觉设计
- **全选项**：灰色背景，圆角边框，突出显示
- **房源项**：带分割线，hover效果
- **确认按钮**：全宽蓝色按钮，禁用时灰色
- **加载状态**：居中显示加载动画

### 3. 交互反馈
- **选择状态**：单选框选中/未选中状态
- **hover效果**：房源项悬停背景色变化
- **按钮反馈**：点击时的视觉反馈
- **计数显示**：确定按钮显示选择数量

## 注意事项

1. **数据同步**：选择状态与数据模型保持同步
2. **性能优化**：大量房源时考虑虚拟滚动
3. **体验优化**：搜索时的防抖处理
4. **状态管理**：弹框关闭时清理所有状态
5. **错误降级**：API失败时的友好降级体验 