<template>
  <div class="test-signature">
    <van-nav-bar title="签字组件测试" left-arrow @click-left="$router.back()" />
    
    <div class="content">
      <van-cell-group>
        <van-cell title="签字状态" :value="signatureUrl ? '已签字' : '未签字'" />
        <van-cell v-if="signatureUrl" title="图片URL" :value="signatureUrl" />
      </van-cell-group>

      <div class="signature-demo">
        <h3>签字演示</h3>
        <SignatureCanvas 
          @confirm="onSignatureConfirm"
          @cancel="onSignatureCancel"
        />
      </div>

      <div class="popup-demo">
        <van-button type="primary" @click="showPopup = true" block>
          弹窗中签字
        </van-button>
      </div>
    </div>

    <!-- 弹窗签字 -->
    <van-popup 
      v-model:show="showPopup" 
      position="bottom" 
      :style="{ height: '70%' }"
      round
      closeable
    >
      <div class="popup-signature">
        <h3>请在弹窗中签字</h3>
        <SignatureCanvas 
          @confirm="onPopupSignature"
          @cancel="showPopup = false"
        />
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { showToast } from 'vant'
import SignatureCanvas from '../components/SignatureCanvas.vue'

const signatureUrl = ref('')
const showPopup = ref(false)

const onSignatureConfirm = (imageUrl: string) => {
  signatureUrl.value = imageUrl
  showToast({
    type: 'success',
    message: '签字成功！'
  })
}

const onSignatureCancel = () => {
  showToast('取消签字')
}

const onPopupSignature = (imageUrl: string) => {
  signatureUrl.value = imageUrl
  showPopup.value = false
  showToast({
    type: 'success',
    message: '弹窗签字成功！'
  })
}
</script>

<style scoped>
.test-signature {
  min-height: 100vh;
  background: #f5f5f5;
}

.content {
  padding: 20px;
}

.signature-demo {
  margin: 30px 0;
  background: white;
  border-radius: 12px;
  padding: 20px;
}

.signature-demo h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #333;
}

.popup-demo {
  margin: 20px 0;
}

.popup-signature {
  padding: 30px;
  text-align: center;
}

.popup-signature h3 {
  margin: 0 0 30px 0;
  font-size: 18px;
  color: #333;
}
</style> 