<template>
    <div class="title">
        <div class="title-left">
            {{ title }}
        </div>
        <div class="title-right" v-if="status">
            <div class="status-badge" :class="getStatusBadgeClass(status)">{{ status }}</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
const props = defineProps<{
    title: string
    status: string
}>()

const getStatusBadgeClass = (status: string) => {
    return status === '草稿' ? 'draft' : 'pending-payment'
}
</script>

<style scoped>
.title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient( 270deg, #FFFFFF 0%, #3582ff2a 100%);
    box-sizing: border-box;
    padding: 16px 16px;
}

.title-left {
    flex: 1;
    font-size: 32px;
    font-weight: 500;
    color: #000;
    /* padding: 20px 30px; */
}

.title-right {
    font-size: 24px;
    color: #fff;
    /* background: #BEBDBC; */
}
.status-badge {
    padding: 6px 16px;
    border-radius: 6px;
}

.status-badge.draft {
    background-color: #BEBDBC;
    color: #fff;
}

.status-badge.pending-payment {
    background-color: #3AC8D4;
    color: #fff;
}
</style>