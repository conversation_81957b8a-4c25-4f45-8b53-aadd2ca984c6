import request from './index'

// 出场单基础信息类型
export interface ExitVo {
  id: string
  projectId: string
  contractId: string
  contractNo: string
  contractUnionId: string
  terminateId: string
  refundId: string
  customerId: string
  customerName: string
  processType: number
  progressStatus: number
  isDiscount: boolean
  discountAmount: number
  discountReason: string
  finalAmount: number
  refundProcessType: number
  payeeName: string
  payeeAccount: string
  bankName: string
  licenseStatus: number
  taxCertStatus: number
  refundApplyType: number
  signType: number
  signAttachments: string
  signTime: string
  copyTime: string
  copyBy: string
  copyByName: string
  settleTime: string
  settleBy: string
  settleByName: string
  createByName: string
  updateByName: string
  isDel: boolean
  contractPurpose: number
  terminateType: number
  terminateDate: string
  terminateRoomName: string
  terminateRoomCount: number
  engineeringCount: number
  financeCount: number
  refundStatus: number // 退款单状态:0-草稿、1-待退款、2-已退款、3-作废、4-隐藏
}

// 出场房间资产信息类型
export interface ExitRoomAssetsVo {
  id: string
  exitId: string
  exitRoomId: string
  category: number
  name: string
  specification: string
  count: number
  status: number // 1-完好, 2-损坏, 3-丢失
  penalty: number
  isAdd: boolean
  remark: string
  createByName: string
  updateByName: string
  isDel: boolean
}

// 出场房间信息类型
export interface ExitRoomVo {
  id: string
  exitId: string
  roomId: string
  roomName: string
  propertyType: number
  parcelName: string
  buildingName: string
  exitDate: string
  rentControl: number
  doorWindowStatus: number // 1-完好, 2-损坏
  doorWindowPenalty: number
  keyHandoverStatus: number // 1-已交齐, 2-未交齐
  keyPenalty: number
  cleaningStatus: number // 1-自行打扫完毕、洁净, 2-保洁及垃圾清理收费
  cleaningPenalty: number
  elecMeterReading: number
  coldWaterReading: number
  hotWaterReading: number
  elecFee: number
  waterFee: number
  pmFee: number
  roomPhotos: string | null
  assetsSituation: string
  remark: string
  isBusinessConfirmed: boolean
  businessConfirmBy: string
  businessConfirmByName: string
  businessConfirmTime: string
  isFinanceConfirmed: boolean
  financeConfirmBy: string
  financeConfirmByName: string
  financeConfirmTime: string | null
  financeConfirmSignature: string | null
  isEngineeringConfirmed: boolean
  engineeringConfirmBy: string
  engineeringConfirmByName: string
  engineeringConfirmTime: string | null
  engineeringConfirmSignature: string | null
  createByName: string
  updateByName: string
  isDel: boolean
  exitRoomAssetsList: ExitRoomAssetsVo[] | null
}

// 保存物业签字请求参数
export interface ExitPropertySignDTO {
  exitId: string
  exitRoomList: ExitRoomDetail[]
  signType: number
  userId: string
  userName: string
  signatureUrl: string
}

// 房间添加DTO
export interface ExitRoomAddDTO {
  id?: string
  exitId: string
  roomId: string
  roomName: string
  propertyType?: number
  parcelName?: string
  buildingName?: string
  exitDate: string
  rentControl?: number
  doorWindowStatus: number
  doorWindowPenalty: number
  keyHandoverStatus: number
  keyPenalty: number
  cleaningStatus: number
  cleaningPenalty: number
  elecMeterReading?: number
  coldWaterReading?: number
  hotWaterReading?: number
  elecFee?: number
  waterFee?: number
  pmFee?: number
  roomPhotos?: string
  assetsSituation?: string
  remark?: string
  isBusinessConfirmed?: boolean
  businessConfirmBy?: string
  businessConfirmByName?: string
  businessConfirmTime?: string
  isFinanceConfirmed?: boolean
  financeConfirmBy?: string
  financeConfirmByName?: string
  financeConfirmTime?: string
  financeConfirmSignature?: string
  isEngineeringConfirmed?: boolean
  engineeringConfirmBy?: string
  engineeringConfirmByName?: string
  engineeringConfirmTime?: string
  engineeringConfirmSignature?: string
  exitRoomAssetsList: ExitRoomAssetsAddDTO[]
  isSubmit: boolean // true-确认, false-暂存
  isDel?: boolean
}

// 房间资产添加DTO
export interface ExitRoomAssetsAddDTO {
  id?: string
  exitId: string
  exitRoomId: string
  category: number
  name: string
  specification?: string
  count: number
  status: number // 1-完好, 2-损坏, 3-丢失
  penalty: number
  isAdd?: boolean
  remark?: string
  isDel?: boolean
}

// 保存客户签字请求参数
export interface ExitCustomerSignDTO {
  exitId: string
  signatureUrl: string
}

// 出场单查询参数
export interface ExitQueryDTO {
  pageNum: number
  pageSize: number
  id?: string
  projectId?: string
  contractId?: string
  contractNo?: string
  contractUnionId?: string
  terminateId?: string
  refundId?: string
  customerId?: string
  customerName?: string
  processType?: number
  progressStatus?: number
  isDiscount?: boolean
  discountAmount?: number
  discountReason?: string
  finalAmount?: number
  refundProcessType?: number
  payeeName?: string
  payeeAccount?: string
  bankName?: string
  licenseStatus?: number
  taxCertStatus?: number
  refundApplyType?: number
  signType?: number
  signAttachments?: string
  signTime?: string
  copyTime?: string
  copyBy?: string
  copyByName?: string
  settleTime?: string
  settleBy?: string
  settleByName?: string
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  isDel?: boolean
  status?: number
  tenantName?: string
  buildingOrRoomName?: string
  buildingOrRoomOrTenantName?: string // 楼栋/房源/承租人（模糊搜索）
  terminateStartDate?: string
  terminateEndDate?: string
  parcelName?: string
  buildingNames?: string[]
  isFinanceUnconfirmed?: boolean
  isEngineeringUnconfirmed?: boolean
  nearDays?: number // 退租日期近3/7/15天
}

// 退租房间详情
export interface ExitRoomDetail {
  id: string
  exitId: string
  roomId: string
  roomName: string
  propertyType: number
  parcelName: string
  buildingName: string
  exitDate: string
  rentControl: number
  doorWindowStatus: number
  doorWindowPenalty: number
  keyHandoverStatus: number
  keyPenalty: number
  cleaningStatus: number
  cleaningPenalty: number
  elecMeterReading: number
  coldWaterReading: number
  hotWaterReading: number
  elecFee: number
  waterFee: number
  pmFee: number
  roomPhotos: string | null
  assetsSituation: string
  remark: string
  isBusinessConfirmed: boolean
  businessConfirmBy: string
  businessConfirmByName: string
  businessConfirmTime: string
  isFinanceConfirmed: boolean
  financeConfirmBy: string
  financeConfirmByName: string
  financeConfirmTime: string | null
  financeConfirmSignature: string | null
  isEngineeringConfirmed: boolean
  engineeringConfirmBy: string
  engineeringConfirmByName: string
  engineeringConfirmTime: string | null
  engineeringConfirmSignature: string | null
  createByName: string
  updateByName: string
  isDel: boolean
  exitRoomAssetsList: any[] | null
}

export interface ExitRoomResponse {
  customerName: string
  roomList: ExitRoomDetail[]
}

/**
 * 获取物业交割详情
 * @param exitId 出场单ID
 * @param confirmType 确认类型: 1-财务确认, 2-工程确认
 */
export const getPropertyDetail = (exitId: string, confirmType: number) => {
  return request.get<ExitRoomResponse>(`/business-rent-rest/exit/propertyDetail`, {
    exitId,
    confirmType
  })
}

/**
 * 保存物业签字
 * @param data 物业签字数据
 */
export const savePropertySign = (data: ExitPropertySignDTO) => {
  return request.post<void>('/business-rent-rest/exit/savePropertySign', data)
}

/**
 * 保存客户签字
 * @param data 客户签字数据
 */
export const saveCustomerSign = (data: ExitCustomerSignDTO) => {
  return request.post('/business-rent-rest/exit/saveCustomerSign', data)
}



// 上传文件响应类型
export interface UploadResponse {
  fileName: string
  fileUrl: string
  url?: string
}

/**
 * 上传图片文件
 * @param file 图片文件
 */
export const uploadImage = (file: File | Blob) => {
  const formData = new FormData()
  formData.append('file', file, `signature_${Date.now()}.png`)
  
  return request.upload<UploadResponse>('/business-rent-rest/common/upload', formData)
}

// 退租费用VO
export interface ContractTerminateCostVo {
  id: string
  contractId: string
  terminateId: string
  costId: string
  costType: number
  startDate: string
  endDate: string
  customerId: string
  customerName: string
  roomId: string
  roomName: string
  subjectId: string
  subjectName: string
  receivableDate: string
  totalAmount: number
  discountAmount: number
  actualReceivable: number
  terminateReceivable: number
  receivedAmount: number
  penaltyAmount: number
  refundAmount: number
  freeId: number
  createByName: string
  updateByName: string
  isDel: boolean
}

// 退场单详情
export interface ExitDetailVo {
  exitInfo: ExitVo
  contractTerminateInfo: {
    id: string
    contractId: string
    unionId: string
    approveStatus: number
    processId: string
    isExit: boolean
    isPart: boolean
    bondReceivedAmount: number
    rentReceivedAmount: number
    rentOverdueAmount: number
    receivedPeriod: string
    overduePeriod: string
    terminateType: number
    terminateDate: string
    terminateReason: string
    otherReasonDesc: string
    hasOtherDeduction: boolean
    otherDeductionDesc: string
    terminateRemark: string
    terminateAttachments: string
    signAttachments: string
    createByName: string
    updateByName: string
    isDel: boolean
    roomList: any[]
    costList: ContractTerminateCostVo[]
    contract: any
  }
  exitRoomList: ExitRoomVo[]
  exitCostList: {
    id: string
    exitId: string
    costId: string
    startDate: string
    endDate: string
    payType: number
    subjectId: string
    subjectName: string
    receivableDate: string
    amount: number
    type: number
    createByName: string
    updateByName: string
    isDel: boolean
  }[]
}

// 获取退场单详情
export const getExitDetail = (exitId: string) => {
  return request.get<ExitDetailVo>('/business-rent-rest/exit/detail', { exitId })
}

// API响应类型
export interface ApiResponse<T> {
    code: number
    msg: string
    data: T
}

// 分页数据类型
export interface PageData<T> {
    total: number
    rows: T[]
}

// 资产列表项类型
export interface AssetItem {
    id: string
    category: number
    name: string
    specification: string
    attachments: string | null
    usageScope: string
    usageScopeList: any[] | null
    remark: string
    createByName: string
    updateByName: string
    isDel: boolean
}

// 资产列表项（带解析后的附件）类型
export interface AssetItemWithParsedAttachments extends Omit<AssetItem, 'attachments'> {
    attachments: Array<{
        fileUrl: string
        fileName: string
    }> | null
}

// 获取出场单资产列表
export const getExitAssetsList = (exitId: string) => {
    return request.get<ApiResponse<PageData<AssetItem>>>('/business-rent-rest/exit/assets/list', { exitId })
}

// 上传签字单请求参数
export interface ExitUploadSignatureDTO {
  exitId: string
  signatureFiles: string
}

// 出场单添加DTO
export interface ExitAddDTO {
  id?: string
  projectId?: string
  contractId?: string
  contractNo?: string
  contractUnionId?: string
  terminateId?: string
  refundId?: string
  customerId?: string
  customerName?: string
  processType?: number
  progressStatus?: number
  isDiscount?: boolean
  discountAmount?: number
  discountReason?: string
  finalAmount?: number
  refundProcessType?: number
  payeeName?: string
  payeeAccount?: string
  bankName?: string
  licenseStatus?: number
  taxCertStatus?: number
  refundApplyType?: number
  signType?: number
  signAttachments?: string
  signTime?: string
  copyTime?: string
  copyBy?: string
  copyByName?: string
  settleTime?: string
  settleBy?: string
  settleByName?: string
  exitCostList: ExitCostVo[]
  isSubmit: boolean // 是否提交: true-确认, false-暂存
  isDel?: boolean
}

// 出场费用结算VO
export interface ExitCostVo {
  id?: string
  exitId: string
  costId: string
  startDate: string
  endDate: string
  payType: number
  subjectId: string
  subjectName: string
  receivableDate: string
  amount: number
  type: number
  remark?: string
  createByName?: string
  updateByName?: string
  isDel?: boolean
}

// 作废出场单请求参数
export interface ExitCancelDTO {
  exitId: string
}

// 房源位置树节点
export interface ParcelTreeVo {
  nodeName: string
  parcelName: string
  buildingName: string
  children?: ParcelTreeVo[]
}

// 固定资产VO
export interface FixedAssetsVo {
  id: string
  category: number // 种类，字典值item_type
  name: string // 物品名称
  specification: string // 规格
  attachments: string // 附件
  usageScope: string // 使用范围，字典值diversification_purpose
  usageScopeList: string[]
  remark: string // 备注
  createByName: string
  updateByName: string
  isDel: boolean
}

// 出场物业交割详情VO
export interface ExitRoomPropertyVo {
  customerName: string
  roomList: ExitRoomVo[]
}

/**
 * 上传签字单接口
 * @param data 上传签字单数据
 */
export const uploadSignature = (data: ExitUploadSignatureDTO) => {
  return request.post<void>('/business-rent-rest/exit/b/uploadSignature', data)
}

/**
 * 保存结算单
 * @param data 结算单数据
 */
export const saveExitSettlement = (data: ExitAddDTO) => {
  return request.post<void>('/business-rent-rest/exit/b/save', data)
}

/**
 * 出场列表查询接口（后台）
 * @param data 查询参数
 */
export const getExitListB = (data: ExitQueryDTO) => {
  return request.post<ExitVo[]>('/business-rent-rest/exit/b/list', data)
}

/**
 * 作废出场单接口
 * @param data 作废出场单数据
 */
export const cancelExit = (data: ExitCancelDTO) => {
  return request.post<void>('/business-rent-rest/exit/b/cancel', data)
}

/**
 * 获取房源位置树
 * @param projectId 项目ID
 */
export const getParcelTree = (projectId: string) => {
  return request.get<ParcelTreeVo[]>('/business-rent-rest/exit/parcelTree', { projectId })
}

/**
 * 查询固定资产列表
 * @param params 查询参数
 */
export const getFixedAssetsList = (params: {
  category?: number
  name?: string
  pageNum: number
  pageSize: number
}) => {
  return request.get<FixedAssetsVo[]>('/business-rent-rest/exit/assets/list', params)
}

// 选择出场办理类型请求参数
export interface ChooseExitProcessTypeDTO {
  exitId: string
  processType: number // 1-先物业交割后续结算, 2-物业交割并结算
}

/**
 * 选择出场办理类型接口
 * @param data 选择办理类型数据
 */
export const chooseExitProcessType = (data: ChooseExitProcessTypeDTO) => {
  return request.post<void>('/business-rent-rest/exit/b/chooseProcessType', data)
}

// 出场办理数据类型
export interface ExitProcessDTO {
  exitId: string
  contractUnionId: string
  exitDate: string
  assetList: ExitAssetDTO[]
  utilityData: ExitUtilityDTO
  attachments: string[]
  signature: string
  remark?: string
}

// 出场资产数据类型
export interface ExitAssetDTO {
  assetName: string
  isNormal: number // 1-正常, 0-损坏
}

// 出场水电度数类型
export interface ExitUtilityDTO {
  electricReading?: string
  electricShare?: string
  waterReading?: string
  gasReading?: string
  coldReading?: string
  hotReading?: string
}

/**
 * 获取出场办理详情
 * @param exitId 出场单ID
 */
export const getExitProcessDetail = (exitId: string) => {
  return request.get<any>(`/business-rent-rest/exit/b/process/${exitId}`)
}

/**
 * 保存出场办理信息
 * @param data 出场办理数据
 */
export const saveExitProcess = (data: ExitProcessDTO) => {
  return request.post<void>('/business-rent-rest/exit/b/process/save', data)
}

/**
 * 提交出场办理
 * @param data 出场办理数据
 */
export const submitExitProcess = (data: ExitProcessDTO) => {
  return request.post<void>('/business-rent-rest/exit/b/process/submit', data)
}

export function saveExitRoom(data: any) {
  return request.post(`/business-rent-rest/exit/b/saveRoom`, data)
}