<script setup lang="ts">
import { ref } from 'vue'

defineProps<{ msg: string }>()

const selectedItems = ref<number[]>([])
const totalAmount = ref(0)

const bills = [
  {
    id: 1,
    type: '租金',
    status: '逾期',
    amount: 10000.00,
    period: '2025-04-01 ～ 2025-04-30',
    dueDate: '2025-04-01'
  },
  {
    id: 2,
    type: '租金',
    status: '逾期',
    amount: 10000.00,
    period: '2025-05-01 ～ 2025-05-30',
    dueDate: '2025-04-01'
  },
  {
    id: 3,
    type: '租金',
    status: '已到期',
    amount: 10000.00,
    period: '2025-05-01 ～ 2025-05-30',
    dueDate: '2025-04-01'
  },
  {
    id: 4,
    type: '租金',
    status: '未到期',
    amount: 10000.00,
    period: '2025-05-01 ～ 2025-05-30',
    dueDate: '2025-04-01'
  }
]

const toggleSelectAll = () => {
  if (selectedItems.value.length === bills.length) {
    selectedItems.value = []
    totalAmount.value = 0
  } else {
    selectedItems.value = bills.map(bill => bill.id)
    totalAmount.value = bills.reduce((sum, bill) => sum + bill.amount, 0)
  }
}

const toggleSelect = (billId: number, amount: number) => {
  const index = selectedItems.value.indexOf(billId)
  if (index === -1) {
    selectedItems.value.push(billId)
    totalAmount.value += amount
  } else {
    selectedItems.value.splice(index, 1)
    totalAmount.value -= amount
  }
}

const formatAmount = (amount: number) => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`
}
</script>

<template>
  <div class="bill-payment">
    <!-- 租户信息 -->
    <div class="tenant-info">
      <div class="info-item">
        <span class="label">租赁单元：</span>
        <div class="value">
          <p>上海万洋科技众创城-77幢101</p>
          <p>上海万洋科技众创城-77幢102</p>
        </div>
      </div>
      <div class="info-item">
        <span class="label">承租方：</span>
        <span class="value">惠州市拾玖电子商务有限公司</span>
      </div>
      <div class="info-item">
        <span class="label">合同周期：</span>
        <span class="value">2025-04-01 ～ 2028-12-30</span>
      </div>
    </div>

    <!-- 账单列表 -->
    <div class="bill-list">
      <div v-for="bill in bills" :key="bill.id" class="bill-item">
        <div class="bill-header">
          <div class="bill-type">{{ bill.type }}</div>
          <div :class="['bill-status', bill.status]">{{ bill.status }}</div>
        </div>
        <div class="bill-content">
          <div class="bill-period">{{ bill.period }}</div>
          <div class="bill-amount">{{ formatAmount(bill.amount) }}</div>
          <div class="bill-due-date">应缴纳日期：{{ bill.dueDate }}</div>
        </div>
        <div class="bill-checkbox">
          <div class="checkbox" :class="{ active: selectedItems.includes(bill.id) }" @click="toggleSelect(bill.id, bill.amount)"></div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-bar">
      <div class="select-all">
        <div class="checkbox" :class="{ active: selectedItems.length === bills.length }" @click="toggleSelectAll"></div>
        <span>全选</span>
      </div>
      <div class="total">
        <span>合计</span>
        <span class="amount">{{ formatAmount(totalAmount) }}</span>
      </div>
      <button class="pay-button" :disabled="selectedItems.length === 0">去支付</button>
    </div>
  </div>
</template>

<style scoped>
.bill-payment {
  background-color: #f1f1f1;
  min-height: 100vh;
  padding: 20px;
}

.tenant-info {
  background: linear-gradient(to bottom, #009AFF, #005FFF);
  border-radius: 20px;
  padding: 20px;
  color: #fff;
  margin-bottom: 20px;
}

.info-item {
  margin-bottom: 15px;
}

.info-item .label {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.8);
}

.info-item .value {
  font-size: 28px;
  font-weight: 600;
  line-height: 1.4;
}

.bill-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.bill-item {
  background: #fff;
  border-radius: 20px;
  padding: 20px;
  position: relative;
}

.bill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.bill-type {
  font-size: 30px;
  font-weight: 500;
  color: #242433;
}

.bill-status {
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 24px;
  color: #fff;
}

.bill-status.逾期 {
  background-color: #FF9428;
}

.bill-status.已到期 {
  background-color: #FF4141;
}

.bill-status.未到期 {
  background-color: #61C0C9;
}

.bill-content {
  border-bottom: 1px solid #EEEEEE;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.bill-period {
  font-size: 26px;
  font-weight: 500;
  color: #000;
  margin-bottom: 10px;
}

.bill-amount {
  font-size: 28px;
  font-weight: 600;
  color: #FF6900;
  text-align: right;
}

.bill-due-date {
  font-size: 24px;
  color: #919199;
}

.bill-checkbox {
  position: absolute;
  right: 20px;
  bottom: 20px;
}

.checkbox {
  width: 30px;
  height: 30px;
  border: 2px solid #B8B8B8;
  border-radius: 15px;
  cursor: pointer;
}

.checkbox.active {
  background-color: #3583FF;
  border-color: #3583FF;
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0px -2px 8px rgba(0, 0, 0, 0.04);
}

.select-all {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 26px;
  font-weight: 500;
  color: #242433;
}

.total {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: 20px;
}

.total span {
  font-size: 26px;
  font-weight: 500;
  color: #242433;
}

.total .amount {
  font-size: 34px;
  color: #FF6900;
}

.pay-button {
  background-color: #3583FF;
  color: #fff;
  border: none;
  border-radius: 45px;
  padding: 15px 40px;
  font-size: 34px;
  font-weight: 500;
  cursor: pointer;
}

.pay-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
