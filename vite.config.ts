import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import type { ServerOptions } from 'vite'
// 直接使用require导入，避免TypeScript错误
// @ts-ignore
// import postcss from './postcss.config.js'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  server: {
    open: false,
    proxy: {
      '/prod-api': {
        target: 'http://************:8570',
        changeOrigin: true,
      },
      '/statics': {
        target: 'http://************:8570',
        changeOrigin: true,
      }
    },
    port: 5552,
    host: true,
    strictPort: true,
  },
  css: {
    // postcss: postcss
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    }
  }
})
