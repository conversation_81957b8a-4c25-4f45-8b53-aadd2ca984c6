{"name": "wyzgpt-h5", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev:dev": "vite --mode dev", "dev:uat": "vite --mode uat", "dev:prod": "vite --mode prod", "build:dev": "vite build --mode dev", "build:uat": "vite build --mode uat", "build:prod": "vite build --mode prod", "uploadDev": "node upload.server.js dev", "uploadUat": "node upload.server.js uat", "uploadProd": "node upload.server.js prod", "uploadProd2": "node upload.server.js prod2", "buildAnduploadDev": "npm run build:dev && npm run uploadDev", "buildAnduploadUat": "npm run build:uat && npm run uploadUat", "buildAnduploadProd": "npm run build:prod && npm run uploadProd && npm run uploadProd2", "buildAnduploadProd2": "npm run build:prod && npm run uploadProd2", "preview": "vite preview"}, "dependencies": {"amfe-flexible": "^2.2.1", "axios": "^1.9.0", "docx-preview": "^0.3.5", "less": "^4.3.0", "postcss-pxtorem": "^6.1.0", "qrcode-generator": "^2.0.2", "ssh2-sftp-client": "^12.0.0", "vant": "^4.9.19", "vconsole": "^3.15.1", "vue": "^3.5.13", "vue-router": "4"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}