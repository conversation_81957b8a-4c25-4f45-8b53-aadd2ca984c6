import http from './index';

// CRM 仪表板统计数据
export interface DashboardStats {
  todayNewCustomers: number;
  basicCustomers: number;
  premiumCustomers: number;
  reminders: {
    type: string;
    count: number;
  }[];
  personalStats: {
    period: string;
    totalAmount: number;
    newCustomers: number;
    premiumCustomers: number;
    basicCustomers: number;
  };
  trendStats: {
    period: string;
    changeRate: number;
    subtitle: string;
    chartData: {
      date: string;
      value: number;
    }[];
  };
}

// 客户信息
export interface Customer {
  id: number;
  name: string;
  phone: string;
  email?: string;
  type: 'basic' | 'premium';
  status: 'active' | 'inactive';
  createTime: string;
  lastContactTime?: string;
  remark?: string;
}

// 客户列表查询参数
export interface CustomerListParams {
  page: number;
  pageSize: number;
  name?: string;
  phone?: string;
  type?: 'basic' | 'premium';
  status?: 'active' | 'inactive';
}

/**
 * 获取 CRM 仪表板数据
 */
export function getDashboardStats() {
  return http.get<DashboardStats>('/crm/dashboard');
}

/**
 * 获取客户列表
 */
export function getCustomerList(params: CustomerListParams) {
  return http.get<{
    list: Customer[];
    total: number;
  }>('/crm/customers', params);
}

/**
 * 添加客户
 */
export function addCustomer(data: Omit<Customer, 'id' | 'createTime'>) {
  return http.post('/crm/customer', data);
}

/**
 * 更新客户信息
 */
export function updateCustomer(id: number, data: Partial<Customer>) {
  return http.put(`/crm/customer/${id}`, data);
}

/**
 * 删除客户
 */
export function deleteCustomer(id: number) {
  return http.delete(`/crm/customer/${id}`);
}

/**
 * 获取客户详情
 */
export function getCustomerDetail(id: number) {
  return http.get<Customer>(`/crm/customer/${id}`);
} 