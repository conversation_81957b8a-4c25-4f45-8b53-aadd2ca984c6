# 登录页面功能说明

## 概述

这个登录页面支持通过URL参数携带token进行自动登录，也支持手动输入token进行登录。登录成功后，token会被存储到本地localStorage中，用于后续的API请求认证。

## 功能特性

### 1. URL参数自动登录
- 支持通过URL中的`token`参数自动登录
- 例如：`http://localhost:5173/login?token=your_access_token`
- 页面会自动提取token并执行登录流程

### 2. 手动输入登录
- 如果URL中没有token参数，会显示手动输入界面
- 用户可以在文本框中粘贴token进行登录

### 3. 登录状态管理
- **加载状态**：显示loading动画和"正在登录中..."提示
- **成功状态**：显示成功图标和"登录成功"提示，自动跳转到首页
- **错误状态**：显示错误图标和具体错误信息，提供重试按钮

### 4. Token存储
- 使用localStorage安全存储token
- 记录登录时间，支持token过期检查
- 提供完整的token管理API

## 使用方法

### 方法1：URL参数登录（推荐）
```
http://your-domain.com/login?token=your_access_token
```

### 方法2：手动输入登录
1. 访问 `http://your-domain.com/login`
2. 在输入框中粘贴token
3. 点击"登录"按钮

## 技术实现

### 文件结构
```
src/
├── views/
│   └── Login.vue          # 登录页面组件
├── utils/
│   └── auth.ts           # 认证工具函数
└── router/
    └── index.ts          # 路由配置
```

### 核心组件

#### Login.vue
- 基于Vue 3 Composition API
- 支持响应式设计
- 完整的状态管理（loading、success、error、manual）
- 优雅的UI动画效果

#### auth.ts 工具函数
```typescript
// 获取token
getToken(): string | null

// 设置token
setToken(token: string): void

// 删除token
removeToken(): void

// 检查是否有token
hasToken(): boolean

// 获取登录时间
getLoginTime(): number | null

// 检查token是否过期
isTokenExpired(maxAge?: number): boolean

// 清除所有认证信息
clearAuth(): void
```

## 路由配置

在 `src/router/index.ts` 中已添加登录路由：

```typescript
{
  path: '/login',
  name: 'Login',
  component: () => import('../views/Login.vue'),
  meta: {
    title: '用户登录'
  }
}
```

## 安全考虑

### Token验证
- 基础格式验证（长度检查）
- 可扩展的验证逻辑
- 支持与后端API集成验证

### 存储安全
- 使用localStorage进行本地存储
- 支持token过期时间管理
- 提供清除认证信息的方法

## 自定义配置

### 修改token验证逻辑
在 `Login.vue` 的 `validateToken` 函数中添加自定义验证：

```typescript
const validateToken = (token: string): boolean => {
  // 基础验证
  if (!token || token.trim().length === 0) {
    return false
  }
  
  // 添加您的自定义验证逻辑
  // 例如：JWT格式验证、特定前缀检查等
  
  return true
}
```

### 集成API验证
在 `performLogin` 函数中添加API调用：

```typescript
const performLogin = async (token: string): Promise<void> => {
  try {
    loading.value = true
    
    if (!validateToken(token)) {
      throw new Error('Token格式不正确')
    }
    
    // 调用后端API验证token
    const response = await fetch('/api/auth/verify', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (!response.ok) {
      throw new Error('Token验证失败')
    }
    
    // 存储token到本地
    setToken(token)
    
    loginStatus.value = 'success'
    
    // 跳转到首页
    setTimeout(() => {
      router.replace('/')
    }, 1500)
    
  } catch (error: any) {
    loginStatus.value = 'error'
    errorMessage.value = error.message || '登录过程中发生错误'
  } finally {
    loading.value = false
  }
}
```

## 样式自定义

登录页面采用现代化设计，支持响应式布局：

- **桌面端**：居中卡片式布局
- **移动端**：全屏适配
- **主题色**：渐变背景，可自定义颜色
- **动画效果**：加载动画、状态切换动画

### 自定义颜色主题
修改 `Login.vue` 中的CSS变量：

```css
.login-container {
  background: linear-gradient(135deg, #your-color-1 0%, #your-color-2 100%);
}

.login-button {
  background: #your-primary-color;
}
```

## 演示

项目根目录提供了 `login-demo.html` 文件，包含：
- 功能介绍
- 使用示例
- 测试token
- 演示链接

启动开发服务器后，访问此文件查看完整演示。

## 测试用例

### 有效token示例
- 简单token：`demo_token_12345678901234567890`
- JWT token：`eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- 长token：`wyzgpt_access_token_1234567890abcd...`

### 测试场景
1. URL参数登录成功
2. URL参数登录失败（无效token）
3. 手动输入登录成功
4. 手动输入登录失败
5. 已登录用户访问登录页（自动跳转）
6. 登录后页面跳转

## 故障排除

### 常见问题

1. **导入路径错误**
   - 确保使用相对路径：`import { setToken } from '../utils/auth'`

2. **路由未配置**
   - 检查 `router/index.ts` 中是否添加了登录路由

3. **Token存储失败**
   - 检查浏览器是否禁用了localStorage

4. **页面跳转失败**
   - 确保首页路由配置正确
   - 检查是否有路由守卫阻止跳转

### 调试方法

1. 打开浏览器开发者工具
2. 查看Console输出
3. 检查localStorage中的token
4. 查看Network请求（如果有API调用）

## 后续开发建议

1. **集成后端API**：添加真实的token验证接口
2. **错误处理增强**：添加更详细的错误分类和处理
3. **安全加固**：添加CSRF保护、请求加密等
4. **用户体验优化**：添加记住登录状态、自动续期等功能
5. **多端适配**：针对移动端进一步优化UI 