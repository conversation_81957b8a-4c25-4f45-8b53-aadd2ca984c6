.page {
  background-color: rgba(224, 224, 224, 1);
  position: relative;
  width: 750px;
  height: 1624px;
  overflow: hidden;
}

.group_1 {
  background-color: rgba(241, 241, 241, 1);
  position: relative;
}

.section_1 {
  background-color: rgba(255, 255, 255, 1);
  padding: 31px 28px 40px 34px;
}

.box_1 {
  margin-left: 8px;
}

.text-wrapper_1 {
  width: 108px;
  height: 34px;
  overflow-wrap: break-word;
  font-size: 0;
  letter-spacing: -0.5600000023841858px;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 34px;
}

.text_1 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 28px;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 34px;
}

.text_2 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 28px;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 34px;
}

.label_1 {
  width: 34px;
  height: 21px;
  margin: 4px 0 9px 437px;
}

.label_2 {
  width: 31px;
  height: 22px;
  margin: 4px 0 8px 10px;
}

.image_1 {
  width: 50px;
  height: 24px;
  margin: 3px 0 7px 10px;
}

.box_2 {
  width: 363px;
  margin: 32px 325px 0 0;
}

.label_3 {
  width: 37px;
  height: 37px;
  margin-bottom: 2px;
}

.text_3 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
  line-height: 36px;
  margin-top: 3px;
}

.section_2 {
  padding-bottom: 31px;
}

.block_1 {
  background-color: rgba(255, 255, 255, 1);
  padding: 20px 29px 24px 31px;
}

.box_3 {
  background-color: rgba(243, 243, 243, 1);
  border-radius: 33px;
  padding: 15px 359px 14px 24px;
}

.image-text_1 {
  width: 307px;
}

.label_4 {
  width: 28px;
  height: 28px;
  margin: 2px 0 7px 0;
}

.text-group_1 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 26px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 37px;
}

.block_2 {
  background-color: rgba(255, 255, 255, 1);
  padding: 29px 30px 0 31px;
}

.text-wrapper_2 {
  width: 689px;
}

.text_4 {
  overflow-wrap: break-word;
  color: rgba(53, 131, 255, 1);
  font-size: 30px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
  line-height: 42px;
}

.text_5 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
}

.text_6 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
}

.text_7 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
}

.box_4 {
  margin: 23px 574px 0 3px;
}

.box_5 {
  background-color: rgba(53, 131, 255, 1);
  width: 112px;
  height: 6px;
}

.block_3 {
  width: 690px;
  align-self: center;
  margin-top: 30px;
}

.box_6 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 30px;
  padding: 10px 31px 13px 29px;
}

.image-text_2 {
  width: 128px;
}

.text-group_2 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 26px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 37px;
}

.thumbnail_1 {
  width: 12px;
  height: 7px;
  margin: 15px 0 15px 0;
}

.box_7 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 30px;
  padding: 10px 27px 13px 24px;
}

.image-text_3 {
  width: 137px;
}

.text-group_3 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 26px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 37px;
}

.image_2 {
  width: 24px;
  height: 20px;
  margin: 9px 0 8px 0;
}

.section_3 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  width: 690px;
  align-self: center;
  margin-top: -1px;
  padding-bottom: 30px;
}

.box_8 {
  width: 690px;
  background: url(./img/SketchPnga7accd1592e0e0ea9873f644c038a9901b4a027e9d37549a351c44616a9e2526.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 19px 30px 19px 29px;
}

.text_8 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
}

.text-wrapper_3 {
  background-color: rgba(190, 189, 188, 1);
  border-radius: 4px;
  margin: 4px 0 3px 0;
  padding: 1px 17px 1px 17px;
}

.text_9 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_4 {
  width: 168px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 20px 492px 0 30px;
}

.text_10 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_11 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_5 {
  width: 197px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 463px 0 30px;
}

.text_12 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_13 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_6 {
  width: 259px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 401px 0 30px;
}

.text_14 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_15 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_7 {
  width: 335px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 325px 0 30px;
}

.text_16 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_17 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_8 {
  width: 168px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 492px 0 30px;
}

.text_18 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_19 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_9 {
  background-color: rgba(53, 131, 255, 1);
  border-radius: 31px;
  margin: 0 30px 0 505px;
  padding: 9px 51px 8px 52px;
}

.text_20 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 26px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 37px;
}

.section_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  width: 690px;
  align-self: center;
  margin-top: 24px;
  padding-bottom: 30px;
}

.box_9 {
  width: 690px;
  background: url(./img/SketchPnga7accd1592e0e0ea9873f644c038a9901b4a027e9d37549a351c44616a9e2526.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 19px 30px 19px 30px;
}

.text_21 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
}

.text-wrapper_10 {
  background-color: rgba(58, 200, 212, 1);
  border-radius: 4px;
  margin: 4px 0 3px 0;
  padding: 1px 13px 1px 14px;
}

.text_22 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.box_10 {
  width: 630px;
  align-self: center;
  margin-top: 23px;
}

.group_2 {
}

.text-wrapper_11 {
  width: 168px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin-right: 167px;
}

.text_23 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_24 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_12 {
  width: 197px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 138px 0 0;
}

.text_25 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_26 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_13 {
  width: 259px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 76px 0 0;
}

.text_27 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_28 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_14 {
  width: 335px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin-top: 10px;
}

.text_29 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_30 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.group_3 {
  border-radius: 11px;
  border: 1px solid rgba(160, 170, 185, 0.4);
  padding-top: 17px;
  margin: 30px 0 26px 0;
}

.label_5 {
  width: 40px;
  height: 40px;
  align-self: center;
}

.text-wrapper_15 {
  background-color: rgba(160, 170, 185, 1);
  border-radius: 0px 0px 11px 11px;
  margin-top: 16px;
  padding: 1px 23px 3px 23px;
}

.text_31 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 20px;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 28px;
}

.text-wrapper_16 {
  width: 168px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 492px 0 30px;
}

.text_32 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_33 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_17 {
  background-color: rgba(53, 131, 255, 1);
  border-radius: 31px;
  margin: 0 30px 0 485px;
  padding: 12px 61px 12px 62px;
}

.text_34 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 26px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 37px;
}

.section_5 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  align-self: center;
  margin-top: 260px;
  width: 690px;
}

.box_11 {
  background: url(./img/SketchPng4ddba6e94441f4fcd08e73cd03ecfb6caf9a42c668b3f9be836f6abc02637efa.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 29px 273px 0 30px;
}

.image-text_4 {
  width: 387px;
}

.section_6 {
  background-color: rgba(251, 120, 29, 1);
  border-radius: 16px;
  width: 6px;
  height: 10px;
  margin-top: 7px;
}

.text-group_4 {
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(36, 36, 51, 1);
  font-size: 30px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
}

.image-wrapper_1 {
  height: 68px;
  background: url(./img/SketchPngcccc2ff3387e4d5bc9ddad591e430dd76c0654f17d0c252c0cd727c1aaf2e235.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 750px;
  position: absolute;
  left: 0;
  top: 1556px;
}

.image_3 {
  width: 750px;
  height: 68px;
}
